
"""
Export Results Page for the WOSS Seismic Analysis Tool.

This module handles the UI rendering for exporting the results.
It follows the principles outlined in rules.md, particularly regarding
the separation of concerns between UI and backend logic.
"""

import streamlit as st
import os
import tempfile
import shutil
import zipfile
import logging
import numpy as np
import pandas as pd
import segyio
from io import BytesIO

# Import common modules
from common.constants import (
    APP_TITLE, EXPORTABLE_ATTR_DISPLAY_NAMES, EXPORTABLE_ATTR_INTERNAL_NAMES,
    ATTR_NAME_MAP, REVERSE_ATTR_NAME_MAP,
    AVAILABLE_OUTPUTS_SINGLE, AVAILABLE_OUTPUTS_MULTI # Added for plotting
)
from common.session_state import initialize_session_state, reset_state
from common.ui_elements import get_suggested_batch_size_for_export

# Import utility functions
from utils.data_utils import load_trace_sample, merge_segy_batch_files # Corrected import
from utils.processing import calculate_woss # For WOSS calculation in plots
from utils.visualization import plot_spectral_descriptors, add_output_to_subplot, plot_multi_trace_section, plot_section_2d # For plotting
from plotly.subplots import make_subplots # For comparative plots

# Import GPU utility functions
from utils.gpu_utils import check_gpu_availability, log_gpu_info

# Import GPU functions if available
try:
    from utils.dlogst_spec_descriptor_gpu import (
        dlogst_spec_descriptor_gpu_2d_chunked,
        dlogst_spec_descriptor_gpu_2d_chunked_mag
    )
    # Check if GPU is actually available and working
    GPU_AVAILABLE = check_gpu_availability()
    if GPU_AVAILABLE:
        logging.info("Successfully imported GPU spectral descriptor functions and verified GPU is working")
        # Log GPU information for debugging
        log_gpu_info()
    else:
        logging.warning("GPU functions imported but GPU is not available or not working correctly")
except ImportError as e:
    logging.warning(f"Could not import GPU functions: {e}")
    GPU_AVAILABLE = False
    # Define dummy functions
    def dlogst_spec_descriptor_gpu_2d_chunked(*args, **kwargs):
        st.error("GPU function dlogst_spec_descriptor_gpu_2d_chunked not available.")
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu_2d_chunked not available.")
    def dlogst_spec_descriptor_gpu_2d_chunked_mag(*args, **kwargs):
        st.error("GPU function dlogst_spec_descriptor_gpu_2d_chunked_mag not available.")
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu_2d_chunked_mag not available.")

def render():
    """Render the export results page UI."""
    # Initialize session state if needed
    initialize_session_state()

    # Store GPU availability in session state
    st.session_state.GPU_AVAILABLE = GPU_AVAILABLE

    # Check if we're in the view results step
    if st.session_state.current_step == "view_results":
        render_view_results()
    # Check if we're in the export configuration step
    elif st.session_state.current_step == "configure_export":
        render_export_configuration()
    # Check if we're in the export process step
    elif st.session_state.current_step == "export_process":
        render_export_process()
    # Check if we're in the download export step
    elif st.session_state.current_step == "download_export":
        render_download_export()
    # Otherwise, redirect to the appropriate step
    else:
        st.warning("Please complete the previous steps first.")
        if st.button("Go to Mode Selection"):
            st.session_state.current_step = "select_mode"
            st.rerun()

def render_export_configuration():
    """Render the export configuration UI."""
    st.header("Step 3.6: Configure AOI Export")

    if not st.session_state.selected_indices:
        st.error("No traces selected for export. Please go back to Step 3.")
        if st.button("Back to Mode Selection"):
            st.session_state.current_step = "select_mode"
            st.rerun()
    else:
        st.info(f"Configuring export for {len(st.session_state.selected_indices)} traces in the AOI.")

        # Determine available attributes for export
        available_for_export_display = EXPORTABLE_ATTR_DISPLAY_NAMES  # Use predefined list
        selected_attrs_display = []
        selected_attrs_internal = []

        try:
            # Placeholder: In a real scenario, you might verify attribute calculation is possible
            # For now, assume all predefined attributes can be calculated/exported.
            pass

        except FileNotFoundError:
            st.error(f"Error: SEG-Y file not found at expected path: {st.session_state.header_loader.temp_file_path}. Please reload data.")
            logging.error(f"SEG-Y file not found at {st.session_state.header_loader.temp_file_path} during export config.")
            reset_state()
            st.rerun()
        except Exception as e:
            st.error(f"Error during export configuration setup: {e}")
            logging.error(f"Export config setup failed: {e}", exc_info=True)
            # Prevent proceeding without attributes
            available_for_export_display = []

        # --- Export Configuration UI ---
        st.subheader("Export Settings")

        # Attribute Selection
        selected_attrs_display = st.multiselect(
            "Select Attributes to Export:",
            options=available_for_export_display,
            default=[attr_name for attr_name in available_for_export_display if attr_name != "Original Seismic Amplitude"]  # Default to all except original
        )
        selected_attrs_internal = [ATTR_NAME_MAP[attr_name] for attr_name in selected_attrs_display]

        # Grouping and Batching
        grouping_type = st.selectbox(
            "Group Export Files By:",
            options=["inline", "crossline"],
            index=0,
            key="export_grouping_select"
        )
        st.session_state.export_grouping = grouping_type  # Store selection

        # Suggest batch size based on grouping
        if st.session_state.header_loader:
            # Create headers_df from the header_loader attributes if it doesn't exist
            if not hasattr(st.session_state.header_loader, 'headers_df'):
                # Create a DataFrame from the header information
                st.session_state.header_loader.headers_df = pd.DataFrame({
                    'inline': st.session_state.header_loader.inlines,
                    'crossline': st.session_state.header_loader.crosslines,
                    'x': st.session_state.header_loader.x_coords,
                    'y': st.session_state.header_loader.y_coords,
                    'trace_idx': st.session_state.header_loader.unique_indices
                })

            headers_df = st.session_state.header_loader.headers_df
            if grouping_type in headers_df.columns:
                num_unique_groups = headers_df[grouping_type].nunique()
                suggested_batch = get_suggested_batch_size_for_export(num_unique_groups)
                st.info(f"Number of unique {grouping_type}s: {num_unique_groups}. Suggested batch step: {suggested_batch}")
            else:
                suggested_batch = 1
                st.warning(f"Column '{grouping_type}' not found in headers. Defaulting batch step to 1.")
        else:
            suggested_batch = 1
            st.warning("Header data not available for suggesting batch size.")

        batch_step = st.number_input(
            f"Batch Step (Number of {grouping_type.capitalize()}s per File)",
            min_value=1,
            value=st.session_state.get('export_batch_step', suggested_batch),  # Use previous or suggested
            step=1,
            key="export_batch_step_input"
        )
        st.session_state.export_batch_step = batch_step  # Store selection

        # GPU Batch Size for Calculation (if GPU available)
        if GPU_AVAILABLE:
            st.session_state.batch_size = st.number_input(
                "GPU Processing Batch Size (Traces)",
                min_value=32,
                max_value=4096,
                value=st.session_state.get('batch_size', 512),  # Default or previous value
                step=32,
                help="Adjust based on GPU memory. Larger values process faster but use more memory."
            )
        else:
            st.warning("GPU not available. Export requires GPU for efficient processing.")

        st.markdown("---")
        # Disable button if GPU needed but not available, or no attributes selected
        disable_export_button = (not GPU_AVAILABLE) or (not selected_attrs_internal)
        if st.button("Start Export Process", key="start_export_button", disabled=disable_export_button):
            if not selected_attrs_internal:
                st.warning("Please select at least one attribute to export.")
            elif not GPU_AVAILABLE:
                st.error("GPU is required for the export process but is not available.")
            else:
                # Store final selections in session state before proceeding
                st.session_state.export_attributes = selected_attrs_internal
                st.session_state.export_grouping = grouping_type
                st.session_state.export_batch_step = batch_step
                # batch_size is already stored above if GPU_AVAILABLE

                st.session_state.current_step = "export_process"
                st.session_state.export_in_progress = True  # Flag to indicate export started
                logging.info(f"Starting export process for attributes: {selected_attrs_internal}, grouping by {grouping_type}, batch step {batch_step}.")
                st.rerun()

def render_export_process():
    """Render the export process UI."""
    st.header("Step 4.5: Processing AOI Export")
    # Check if export was actually started
    if not st.session_state.get('export_in_progress', False):
        st.warning("Export process not initiated correctly. Please configure export first.")
        if st.button("Go to Export Configuration"):
            st.session_state.current_step = "configure_export"
            st.rerun()
        # Add a safety break
        return

    # Use context manager for temporary directories
    with tempfile.TemporaryDirectory() as temp_batch_dir, \
         tempfile.TemporaryDirectory() as temp_export_dir:

        st.info(f"Using temporary batch directory: `{temp_batch_dir}`")
        st.info(f"Using temporary export directory: `{temp_export_dir}`")
        status_text_export = st.empty()
        batch_file_log_path = os.path.join(temp_export_dir, "batch_creation_log.txt")  # Log batch creation details

        try:
            # Get necessary info from session state
            selected_indices = st.session_state.selected_indices
            selected_attrs = st.session_state.export_attributes
            grouping_type = st.session_state.export_grouping
            batch_step = st.session_state.export_batch_step
            headers_df = st.session_state.header_loader.headers_df
            # Use the actual path (could be temp) from the loader object
            segy_path = st.session_state.header_loader.source_file_path  # Use the stored path

            # Log information about the selected AOI
            logging.info(f"Processing export with {len(selected_indices)} traces selected from AOI")

            # --- Export Setup ---
            if grouping_type not in headers_df.columns:
                st.error(f"Grouping column '{grouping_type}' not found in headers. Aborting export.")
                raise ValueError(f"Grouping column '{grouping_type}' not found.")

            unique_groups = sorted(headers_df[grouping_type].unique())
            if not unique_groups:
                st.error(f"No unique groups found for '{grouping_type}'. Aborting export.")
                raise ValueError(f"No unique groups found for '{grouping_type}'.")

            if batch_step <= 0:
                st.warning("Batch step must be positive. Setting to 1.")
                batch_step = 1

            # Process groups in batches
            num_groups_processed = 0
            overall_progress = st.progress(0, text=f"Starting export process...")
            all_batch_descriptors_list = []  # Store descriptors if needed later, maybe not necessary

            # Initialize dictionary to store batch filenames per attribute
            all_batch_files = {attr: [] for attr in selected_attrs}

            # Open batch log file
            with open(batch_file_log_path, "w") as batch_file_log:
                batch_file_log.write("--- Batch File Creation Log ---\n\n")

                for i in range(0, len(unique_groups), batch_step):
                    # --- Batch Setup ---
                    batch_start_index = i
                    batch_end_index = min(i + batch_step, len(unique_groups))
                    current_batch_groups = unique_groups[batch_start_index:batch_end_index]
                    batch_start_group = current_batch_groups[0]
                    batch_end_group = current_batch_groups[-1]
                    # --- End Batch Setup ---

                    status_text_export.text(f"Processing batch: {grouping_type.capitalize()}s {batch_start_group} to {batch_end_group}...")
                    batch_file_log.write(f"Processing Batch: {grouping_type.capitalize()}s {batch_start_group}-{batch_end_group}\n")

                    # Find trace indices for this batch, filtered by selected AOI
                    # Use the approach from the reference implementation
                    batch_indices = []
                    for group_value in current_batch_groups:
                        group_mask = headers_df[grouping_type] == group_value
                        aoi_mask = headers_df['trace_idx'].isin(selected_indices)
                        combined_mask = group_mask & aoi_mask
                        group_indices = headers_df.loc[combined_mask, 'trace_idx'].tolist()
                        batch_indices.extend(group_indices)
                    batch_file_log.write(f"  Indices: {len(batch_indices)} traces (filtered by AOI)\n")

                    if not batch_indices:
                        batch_file_log.write("  - Skipping batch (no traces found).\n")
                        continue

                    # Load trace data for the batch
                    batch_trace_data = []
                    with st.spinner(f"Loading {len(batch_indices)} traces for batch {batch_start_group}-{batch_end_group}..."):
                        try:
                            # Determine max length for padding
                            max_len = 0
                            for trace_idx in batch_indices:
                                trace = load_trace_sample(segy_path, trace_idx)
                                batch_trace_data.append({'trace_sample': trace, 'trace_idx': trace_idx})
                                max_len = max(max_len, len(trace))

                            # Pad traces if necessary (GPU function might require uniform length)
                            # --- Padding Logic ---
                            if (max_len > 0):
                                for item in batch_trace_data:
                                    if len(item['trace_sample']) < max_len:
                                        pad_width = max_len - len(item['trace_sample'])
                                        item['trace_sample'] = np.pad(item['trace_sample'], (0, pad_width), 'constant')
                            # --- End Padding Logic ---

                        except FileNotFoundError:
                            st.error(f"Error: SEG-Y file not found at {segy_path} during batch loading. Aborting export.")
                            logging.error(f"SEG-Y file not found at {segy_path} during export batch loading.")
                            raise  # Re-raise to stop the export
                        except Exception as load_e:
                            st.error(f"Error loading traces for batch {batch_start_group}-{batch_end_group}: {load_e}")
                            logging.error(f"Trace loading failed for batch {batch_start_group}-{batch_end_group}: {load_e}", exc_info=True)
                            batch_file_log.write(f"  - ERROR loading traces: {load_e}. Skipping batch.\n")
                            continue  # Skip this batch

                    if not batch_trace_data:
                        batch_file_log.write("  - Skipping batch (failed to load trace data).\n")
                        continue

                    # Calculate descriptors for the batch
                    batch_descriptors = []  # List of dictionaries, one per trace
                    with st.spinner(f"Calculating descriptors for batch {batch_start_group}-{batch_end_group}..."):
                        # --- Descriptor Settings Setup ---
                        descriptor_settings = {
                            'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                            'shape': st.session_state.plot_settings.get('shape', 0.35),
                            'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                            'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                            'b1': st.session_state.plot_settings.get('b1', 5.0),
                            'b2': st.session_state.plot_settings.get('b2', 40.0),
                            'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                            'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80)
                        }
                        # --- End Descriptor Settings Setup ---
                        try:
                            if GPU_AVAILABLE:
                                if not batch_trace_data:
                                    raise ValueError("No trace data available for GPU processing.")
                                # Stack traces for GPU processing
                                arr = np.stack([t['trace_sample'] for t in batch_trace_data]).astype(np.float32)
                                fmax_calc = max_len // 2 if max_len > 0 else 250  # Default fmax if max_len is 0

                                descriptors_all_dict = dlogst_spec_descriptor_gpu_2d_chunked(
                                    arr, st.session_state.dt, fmax=fmax_calc,
                                    batch_size=st.session_state.batch_size or 512,
                                    **descriptor_settings
                                )
                                # --- Unpack Descriptors ---
                                num_traces_in_batch = arr.shape[0]
                                batch_descriptors = [{} for _ in range(num_traces_in_batch)]
                                for key, value_array in descriptors_all_dict.items():
                                    if value_array.shape[0] == num_traces_in_batch:
                                        for trace_idx in range(num_traces_in_batch):
                                            batch_descriptors[trace_idx][key] = value_array[trace_idx]
                                    else:
                                        logging.warning(f"Shape mismatch for descriptor '{key}' in batch {batch_start_group}-{batch_end_group}. Expected {num_traces_in_batch} traces, got {value_array.shape[0]}. Skipping this descriptor for the batch.")
                                        batch_file_log.write(f"  - WARNING: Shape mismatch for descriptor '{key}'. Skipping.\n")
                                # --- End Unpack Descriptors ---

                                # Calculate WOSS if requested
                                if 'WOSS' in selected_attrs:
                                    # Check if hfc_p95 is available in plot_settings
                                    if 'hfc_p95' not in st.session_state.plot_settings:
                                        logging.warning("HFC p95 value not found in plot_settings for WOSS calculation during export.")
                                        # Calculate HFC p95 from the current batch data if possible
                                        all_hfc_values = []
                                        for desc in batch_descriptors:
                                            if 'hfc' in desc and isinstance(desc['hfc'], np.ndarray) and desc['hfc'].size > 0:
                                                all_hfc_values.extend(desc['hfc'])

                                        if all_hfc_values:
                                            hfc_percentile = st.session_state.plot_settings.get('hfc_percentile', 95.0)
                                            hfc_p95 = np.percentile(np.array(all_hfc_values), hfc_percentile)
                                            st.session_state.plot_settings['hfc_p95'] = float(hfc_p95)
                                            logging.info(f"Calculated HFC p{hfc_percentile} from batch data for export: {hfc_p95}")
                                        else:
                                            # Import the helper function
                                            from utils.processing import get_robust_hfc_normalization_value

                                            # Use robust fallback instead of hardcoded 1.0
                                            fallback_hfc_p95, source_description = get_robust_hfc_normalization_value()
                                            st.session_state.plot_settings['hfc_p95'] = fallback_hfc_p95
                                            logging.warning(f"Could not calculate HFC p95 from batch data. Using fallback: {fallback_hfc_p95} (source: {source_description})")

                                    # Log the HFC p95 value being used
                                    hfc_p95 = st.session_state.plot_settings.get('hfc_p95', 1.0)
                                    logging.info(f"Using HFC p95 value for WOSS calculation during export: {hfc_p95}")

                                    # Get other WOSS parameters
                                    epsilon = st.session_state.plot_settings.get('epsilon', 1e-10)
                                    fdom_exponent = st.session_state.plot_settings.get('fdom_exponent', 2.0)

                                    # Create a copy of plot_settings to ensure hfc_p95 is included
                                    woss_params = st.session_state.plot_settings.copy()
                                    woss_params['hfc_p95'] = hfc_p95
                                    woss_params['epsilon'] = epsilon
                                    woss_params['fdom_exponent'] = fdom_exponent

                                    # Calculate WOSS for each descriptor
                                    for desc_idx, desc in enumerate(batch_descriptors):
                                        try:
                                            # Check if descriptor has required keys for WOSS calculation
                                            if not all(k in desc for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                                                missing_keys = [k for k in ['hfc', 'norm_fdom', 'mag_voice_slope'] if k not in desc]
                                                logging.warning(f"Cannot calculate WOSS for trace index {batch_trace_data[desc_idx]['trace_idx']} - missing keys: {missing_keys}")
                                                batch_file_log.write(f"  - WARNING: Cannot calculate WOSS for trace index {batch_trace_data[desc_idx]['trace_idx']} - missing keys: {missing_keys}\n")
                                                sample_len = len(batch_trace_data[desc_idx]['trace_sample'])
                                                desc['WOSS'] = np.zeros(sample_len, dtype=np.float32)
                                                continue

                                            # Use calculate_woss helper with explicit parameters
                                            from utils.processing import calculate_woss
                                            desc['WOSS'] = calculate_woss(desc, woss_params)
                                            logging.info(f"Successfully calculated WOSS for trace index {batch_trace_data[desc_idx]['trace_idx']}")
                                        except Exception as woss_e:
                                            logging.warning(f"Could not calculate WOSS for trace index {batch_trace_data[desc_idx]['trace_idx']} in batch: {woss_e}")
                                            batch_file_log.write(f"  - WARNING: Failed WOSS calculation for trace index {batch_trace_data[desc_idx]['trace_idx']}: {woss_e}\n")
                                            # Assign default value if calculation fails
                                            sample_len = len(batch_trace_data[desc_idx]['trace_sample'])
                                            desc['WOSS'] = np.zeros(sample_len, dtype=np.float32)

                            else:
                                st.error("GPU required for efficient export. Aborting.")
                                logging.error("GPU required for export, but not available.")
                                raise RuntimeError("GPU required for export.")
                        except Exception as desc_e:
                            st.error(f"Error calculating descriptors for batch {batch_start_group}-{batch_end_group}: {desc_e}")
                            logging.error(f"Descriptor calculation failed for batch {batch_start_group}-{batch_end_group}: {desc_e}", exc_info=True)
                            batch_file_log.write(f"  - ERROR calculating descriptors: {desc_e}. Skipping batch.\n")
                            continue  # Skip this batch

                    # Export each selected attribute for this batch
                    with st.spinner(f"Writing batch files for {grouping_type}s {batch_start_group}-{batch_end_group}..."):
                        try:
                            with segyio.open(segy_path, 'r', ignore_geometry=True) as src:
                                spec = segyio.spec()
                                spec.format = src.format  # Use source format
                                spec.samples = range(max_len) if max_len > 0 else src.samples  # Use padded length or original
                                spec.tracecount = len(batch_indices)  # Set tracecount for the batch file

                                for attr in selected_attrs:
                                    display_name = REVERSE_ATTR_NAME_MAP.get(attr, attr)
                                    batch_output_filename = f"batch_{batch_start_group}_{batch_end_group}_{attr}.sgy"
                                    batch_output_path = os.path.join(temp_batch_dir, batch_output_filename)
                                    batch_file_log.write(f"  - Creating: {batch_output_filename}\n")
                                    # Add batch file to the tracking dictionary
                                    all_batch_files[attr].append(batch_output_path)

                                    with segyio.create(batch_output_path, spec) as dst:
                                        # --- Copy Headers and Write Trace Data ---
                                        for i, (trace_item, descriptor) in enumerate(zip(batch_trace_data, batch_descriptors)):
                                            original_trace_idx = trace_item['trace_idx']
                                            # Copy header from original file
                                            dst.header[i] = src.header[original_trace_idx]

                                            # Get the calculated attribute data
                                            trace_to_write = None
                                            if attr == 'data':  # Handle original data export
                                                trace_to_write = trace_item['trace_sample']
                                            elif descriptor and attr in descriptor:
                                                trace_to_write = descriptor[attr]
                                            else:
                                                batch_file_log.write(f"    - Warning: Attribute '{attr}' missing/invalid for trace index {original_trace_idx}. Writing zeros.\n")
                                                logging.warning(f"Attribute '{attr}' missing for trace index {original_trace_idx} in batch {batch_start_group}-{batch_end_group}.")

                                            # Ensure data is float32 and correct length, write zeros if missing/error
                                            if trace_to_write is not None:
                                                if len(trace_to_write) == max_len:
                                                    dst.trace[i] = trace_to_write.astype(np.float32)
                                                else:
                                                    batch_file_log.write(f"    - Warning: Length mismatch for '{attr}' on trace index {original_trace_idx} (expected {max_len}, got {len(trace_to_write)}). Writing zeros.\n")
                                                    logging.warning(f"Length mismatch for '{attr}' on trace index {original_trace_idx}. Expected {max_len}, got {len(trace_to_write)}.")
                                                    dst.trace[i] = np.zeros(max_len, dtype=np.float32)
                                            else:
                                                # Write zeros if attribute was missing
                                                dst.trace[i] = np.zeros(max_len, dtype=np.float32)
                                        # --- End Copy Headers and Write Trace Data ---

                        except Exception as write_e:
                            st.error(f"Error writing batch files for {batch_start_group}-{batch_end_group}: {write_e}")
                            logging.error(f"Batch file writing failed for batch {batch_start_group}-{batch_end_group}: {write_e}", exc_info=True)
                            batch_file_log.write(f"  - ERROR writing batch files: {write_e}. Skipping batch output.\n")
                            # Decide if to continue with next batch or abort
                            continue  # Continue to next batch for now

                    # Update progress
                    num_groups_processed += len(current_batch_groups)
                    progress_percentage = num_groups_processed / len(unique_groups)
                    overall_progress.progress(progress_percentage, text=f"Processed {num_groups_processed}/{len(unique_groups)} {grouping_type} groups...")
                    # Clear memory (optional, Python's GC usually handles it)
                    del batch_trace_data, batch_descriptors, arr  # If memory is tight

            status_text_export.text("Batch processing complete. Merging files...")
            overall_progress.progress(1.0, text="Merging batch files...")  # Show merging progress

            # Merge batch files for each attribute
            final_merged_files_info = []
            merge_log_content = ""
            with st.spinner("Merging batch files for each attribute..."):
                for attr in selected_attrs:
                    # --- Merging Logic ---
                    display_name = REVERSE_ATTR_NAME_MAP.get(attr, attr)
                    final_output_filename = f"merged_{st.session_state.segy_file_info['name']}_{attr}.sgy"
                    final_output_path = os.path.join(temp_export_dir, final_output_filename)

                    # Use the explicit list of batch files from the tracking dictionary
                    batch_files = all_batch_files[attr]

                    # Filter out any files that might not exist (safety check)
                    batch_files = [f for f in batch_files if os.path.exists(f)]

                    logging.info(f"Merging {len(batch_files)} batch files for attribute {attr}")
                    logging.info(f"Outputting merged file to: {final_output_path}")
                    # --- End Merging Logic ---

                    # Call merge_segy_batch_files with only the required parameters
                    merge_log = merge_segy_batch_files(batch_files, final_output_path)
                    merge_log_content += f"Merge Log for {display_name}:\n{merge_log}\n\n"
                    # Store info needed for download (attribute name, final path in temp_export_dir)
                    if os.path.exists(final_output_path):  # Check if merge was successful
                        final_merged_files_info.append({"attribute": display_name, "path": final_output_path})
                        logging.info(f"Successfully merged batch files for {attr} into {final_output_path}")
                    else:
                        logging.error(f"Merging failed for attribute {attr}. Output file not found: {final_output_path}")
                        st.error(f"Merging failed for attribute: {display_name}. See logs for details.")

            # Save the merge log
            merge_log_path = os.path.join(temp_export_dir, "merge_log.txt")
            with open(merge_log_path, "w") as f:
                f.write(merge_log_content)
                f.write("\n--- Batch Creation Log ---\n")
                # Append batch creation log content
                try:
                    with open(batch_file_log_path, "r") as bf_log:
                        f.write(bf_log.read())
                except Exception:
                    f.write("Error reading batch creation log.")

            logging.info(f"Combined merge and batch log saved to {merge_log_path}")
            # Add log file to download list only if it exists
            if os.path.exists(merge_log_path):
                final_merged_files_info.append({"attribute": "Processing Log", "path": merge_log_path})
            else:
                logging.warning("Merge log file was not created.")

            # Don't clean up temp_batch_dir here, it's handled by context manager

            status_text_export.success("Export and merge process complete!")
            overall_progress.empty()
            st.session_state.export_in_progress = False
            # Store results for download step - paths are inside temp_export_dir
            st.session_state.exported_files_info = final_merged_files_info
            # Need to copy files out of temp_export_dir or keep it alive until download
            # Let's copy to a more persistent temp dir for the download step
            persistent_export_dir = tempfile.mkdtemp(prefix="woss_export_")
            final_downloadable_files = []
            for file_info in final_merged_files_info:
                try:
                    if os.path.exists(file_info['path']):  # Check if file exists before copying
                        base_name = os.path.basename(file_info['path'])
                        new_path = os.path.join(persistent_export_dir, base_name)
                        shutil.copy2(file_info['path'], new_path)
                        final_downloadable_files.append({"attribute": file_info['attribute'], "path": new_path})
                    else:
                        st.warning(f"Skipping copy for non-existent file: {file_info['path']}")
                        logging.warning(f"File not found, skipping copy to persistent dir: {file_info['path']}")
                except Exception as copy_e:
                    st.warning(f"Could not prepare file for download {os.path.basename(file_info.get('path','N/A'))} : {copy_e}")
                    logging.warning(f"Failed to copy {file_info['path']} to {persistent_export_dir}: {copy_e}")

            st.session_state.exported_files_info = final_downloadable_files  # Update with new paths
            st.session_state.export_output_dir = persistent_export_dir  # Store the persistent directory path
            st.session_state.current_step = "download_export"
            st.rerun()

        except FileNotFoundError as fnf_err:
            # Handle file not found during the main process (already logged likely)
            st.error(f"Export failed: Required file not found. {fnf_err}")
            logging.error(f"Export process failed due to FileNotFoundError: {fnf_err}", exc_info=True)
            st.session_state.export_in_progress = False
        except Exception as e:
            st.error(f"An error occurred during the export process: {e}")
            logging.error(f"Export process failed: {e}", exc_info=True)
            st.session_state.export_in_progress = False
            # Context managers will clean up temp_batch_dir and temp_export_dir
            if st.button("Back to Configuration"):
                st.session_state.current_step = "configure_export"
                st.rerun()

def render_view_results():
    """Render the view results UI."""
    st.header("Step 5: View Analysis Results")

    # Add detailed logging for debugging
    logging.info("Starting render_view_results function")

    # Check if analysis is complete
    if not st.session_state.get('analysis_complete', False):
        logging.warning("Analysis not complete. Redirecting to analysis page.")
        st.warning("Analysis is not yet complete. Please go back to Step 4 and complete the analysis.")
        if st.button("Back to Analysis"):
            st.session_state.current_step = "analyze_data"
            st.rerun()
        return

    # Check if we have calculated descriptors
    calculated_descriptors = st.session_state.get('calculated_descriptors', [])
    if not calculated_descriptors:
        logging.warning("No calculated descriptors found in session state.")

        # Check if we have individual analysis results that weren't converted
        individual_results = st.session_state.get('individual_well_analysis_results', [])
        if individual_results:
            st.warning("Individual analysis results found but not properly converted. Attempting to fix...")
            logging.info("Attempting to convert individual analysis results to calculated_descriptors format")

            # Convert individual results to the expected format
            converted_descriptors = []
            for result in individual_results:
                if 'descriptors' in result and result['descriptors']:
                    descriptor_copy = result['descriptors'].copy()
                    descriptor_copy['well_marker_name'] = result.get('well_marker_name', 'Unknown')
                    descriptor_copy['trace_idx'] = result.get('trace_idx')
                    converted_descriptors.append(descriptor_copy)
                else:
                    converted_descriptors.append({
                        'error': result.get('error', 'Unknown error'),
                        'well_marker_name': result.get('well_marker_name', 'Unknown'),
                        'trace_idx': result.get('trace_idx')
                    })

            st.session_state.calculated_descriptors = converted_descriptors
            calculated_descriptors = converted_descriptors
            st.success("Successfully converted individual analysis results!")
        else:
            st.warning("No calculated descriptors found. Please go back to Step 4 and complete the analysis.")
            if st.button("Back to Analysis"):
                st.session_state.current_step = "analyze_data"
                st.rerun()
            return

    # Log information about the calculated descriptors
    valid_descriptors = [desc for desc in calculated_descriptors if desc and 'error' not in desc]
    error_descriptors = [desc for desc in calculated_descriptors if desc and 'error' in desc]
    logging.info(f"Found {len(valid_descriptors)} valid descriptors and {len(error_descriptors)} error descriptors out of {len(calculated_descriptors)} total")

    if valid_descriptors:
        # Log keys in the first valid descriptor for debugging
        first_desc = valid_descriptors[0]
        logging.info(f"First descriptor keys: {list(first_desc.keys())}")

        # Check for required keys for visualization - be more flexible about required keys
        # Different analysis modes may have different key structures
        basic_keys = ['data']  # Only require the most basic key
        missing_keys = [key for key in basic_keys if key not in first_desc]
        if missing_keys:
            logging.warning(f"First descriptor is missing basic required keys: {missing_keys}")

        # Log available keys for debugging
        available_keys = list(first_desc.keys())
        logging.info(f"Available descriptor keys: {available_keys}")

    # Display the results with more detailed information
    if error_descriptors:
        st.warning(f"Analysis complete with some issues! Found {len(valid_descriptors)} valid results and {len(error_descriptors)} failed traces out of {len(calculated_descriptors)} total.")

        # Show error details in an expander
        with st.expander("View Error Details", expanded=False):
            for i, desc in enumerate(error_descriptors):
                st.error(f"Trace {desc.get('well_marker_name', 'Unknown')} (Index: {desc.get('trace_idx', 'Unknown')}): {desc.get('error', 'Unknown error')}")
    else:
        st.success(f"Analysis complete! Displaying results for {len(calculated_descriptors)} traces ({len(valid_descriptors)} valid).")

    # Add a fallback visualization for when descriptor calculations fail
    if not valid_descriptors:
        st.error("No valid descriptors found. Unable to display results.")
        st.info("This could be due to GPU calculation failures or missing data. Check the logs for more details.")

        # Suggest solutions
        st.markdown("""
        ### Troubleshooting suggestions:
        1. **Check GPU availability**: Make sure your GPU is properly configured and CUDA is installed
        2. **Try CPU fallback**: The application should automatically fall back to CPU implementation, but this might be failing too
        3. **Check input data**: Ensure the input data is valid and properly formatted
        4. **Restart the application**: Sometimes a fresh start can resolve issues
        """)

        # Add a button to go back to analysis
        if st.button("Retry Analysis", key="retry_analysis_button"):
            st.session_state.current_step = "analyze_data"
            st.rerun()
        return

    # --- Debug Information Section ---
    with st.expander("🔍 Debug Information", expanded=False):
        st.markdown("### Session State Debug Info")
        st.write(f"**Selection Mode:** {st.session_state.get('selection_mode', 'Not set')}")
        st.write(f"**Plot Mode Wells:** {st.session_state.get('plot_mode_wells', 'Not set')}")
        st.write(f"**Well Analysis Sub-option:** {st.session_state.get('well_analysis_sub_option', 'Not set')}")
        st.write(f"**Analysis Complete:** {st.session_state.get('analysis_complete', False)}")

        st.markdown("### Data Availability")
        st.write(f"**Loaded Trace Data:** {len(st.session_state.get('loaded_trace_data', []))} items")
        st.write(f"**Calculated Descriptors:** {len(calculated_descriptors)} items")
        st.write(f"**Valid Descriptors:** {len(valid_descriptors)} items")
        st.write(f"**Error Descriptors:** {len(error_descriptors)} items")
        st.write(f"**Individual Analysis Results:** {len(st.session_state.get('individual_well_analysis_results', []))} items")

        if valid_descriptors:
            st.markdown("### First Valid Descriptor Keys")
            st.code(str(list(valid_descriptors[0].keys())))

        st.markdown("### Plot Settings")
        plot_settings = st.session_state.get('plot_settings', {})

        # Display normalization cutoff values using simplified approach

        # Get HFC percentile cutoff value for debug display
        hfc_pc = plot_settings.get('hfc_pc')
        if hfc_pc is None:
            hfc_pc = plot_settings.get('hfc_p95', 'Not set')
        hfc_percentile = plot_settings.get('hfc_percentile', 95.0)
        st.write(f"**HFC p{int(hfc_percentile)} cutoff:** {hfc_pc}")

        # Get Spectral Decrease percentile cutoff value for debug display
        spec_decrease_pc = plot_settings.get('spec_decrease_pc')
        if spec_decrease_pc is None:
            spec_decrease_pc = plot_settings.get('spec_decrease_p95', 'Not set')
        spec_decrease_percentile = plot_settings.get('spec_decrease_percentile', 95.0)
        st.write(f"**Spectral Decrease p{int(spec_decrease_percentile)} cutoff:** {spec_decrease_pc}")

        st.write(f"**DT (sampling interval):** {st.session_state.get('dt', 'Not set')}")

    # --- Output Selection ---
    st.markdown("---")
    st.subheader("Select Outputs to Display")

    if st.session_state.selection_mode == "By well markers":
        if st.session_state.plot_mode_wells == 1: # Individual plots
            available_outputs_for_selection = AVAILABLE_OUTPUTS_SINGLE
        else: # Comparative plots
            available_outputs_for_selection = AVAILABLE_OUTPUTS_MULTI
    elif st.session_state.selection_mode in ["Single inline (all crosslines)", "Single crossline (all inlines)", "By Polyline File Import"]:
        available_outputs_for_selection = st.session_state.get('AVAILABLE_OUTPUTS_SECTION', AVAILABLE_OUTPUTS_MULTI) # Fallback if not set
    else: # Default or other modes
        available_outputs_for_selection = AVAILABLE_OUTPUTS_MULTI

    default_selection = st.session_state.get('selected_outputs_view', [])
    default_selection = [out for out in default_selection if out in available_outputs_for_selection]
    if not default_selection and available_outputs_for_selection:
        default_selection = [available_outputs_for_selection[0]] if available_outputs_for_selection else []


    col1_view, col2_view = st.columns([1,3])
    with col1_view:
        if st.button("Select All Outputs", key="select_all_outputs_view_btn"):
            st.session_state.selected_outputs_view = available_outputs_for_selection
            st.rerun() # Rerun to update multiselect with all options selected

    with col2_view:
        selected_outputs = st.multiselect(
            "Choose outputs to display:",
            options=available_outputs_for_selection,
            default=st.session_state.get('selected_outputs_view', default_selection),
            key="view_results_output_selector"
        )
    st.session_state.selected_outputs_view = selected_outputs


    if not selected_outputs:
        st.warning("Please select at least one output to display.")
    else:
        # --- Plotting Logic ---
        try:
            with st.spinner("Generating plots..."):
                # Mode 1: Well Markers
                if st.session_state.selection_mode == "By well markers":
                    plot_settings_copy = st.session_state.plot_settings.copy()
                    plot_settings_copy['selected_outputs'] = selected_outputs # Pass selected outputs to plotting functions
                    plot_settings_copy['is_well_marker_mode'] = True # Flag for x-axis standardization

                    # Ensure percentile cutoff values are available in plot_settings_copy
                    # These should have been calculated in the 'analyze_data' step and stored in st.session_state.plot_settings

                    # Ensure HFC percentile cutoff value is available
                    if 'hfc_pc' not in plot_settings_copy and 'hfc_p95' not in plot_settings_copy:
                        # Fallback to default if no percentile cutoff value is available
                        plot_settings_copy['hfc_pc'] = 1.0
                        logging.warning("Export: No HFC percentile cutoff value found, using fallback value: 1.0")
                    elif 'hfc_pc' not in plot_settings_copy:
                        # Use old naming for backward compatibility
                        plot_settings_copy['hfc_pc'] = plot_settings_copy.get('hfc_p95', 1.0)
                        logging.info(f"Export: Using HFC cutoff value from hfc_p95: {plot_settings_copy['hfc_pc']}")

                    # Ensure Spectral Decrease percentile cutoff value is available
                    if 'spec_decrease_pc' not in plot_settings_copy and 'spec_decrease_p95' not in plot_settings_copy:
                        # Fallback to default if no percentile cutoff value is available
                        plot_settings_copy['spec_decrease_pc'] = 1.0
                        logging.warning("Export: No Spectral Decrease percentile cutoff value found, using fallback value: 1.0")
                    elif 'spec_decrease_pc' not in plot_settings_copy:
                        # Use old naming for backward compatibility
                        plot_settings_copy['spec_decrease_pc'] = plot_settings_copy.get('spec_decrease_p95', 1.0)
                        logging.info(f"Export: Using Spectral Decrease cutoff value from spec_decrease_p95: {plot_settings_copy['spec_decrease_pc']}")


                    if st.session_state.plot_mode_wells == 1: # Individual Plots
                        # Get loaded trace data
                        loaded_trace_data = st.session_state.get('loaded_trace_data', [])

                        # Handle case where we have calculated_descriptors but no loaded_trace_data
                        # This can happen in individual analysis mode
                        if not loaded_trace_data and calculated_descriptors:
                            logging.info("No loaded_trace_data found, attempting to reconstruct from calculated_descriptors")
                            # Try to reconstruct trace data from descriptors
                            loaded_trace_data = []
                            for desc in calculated_descriptors:
                                if 'error' not in desc and 'data' in desc:
                                    trace_info = {
                                        'trace_sample': desc['data'],
                                        'trace_idx': desc.get('trace_idx'),
                                        'well_marker_name': desc.get('well_marker_name', 'Unknown')
                                    }
                                    loaded_trace_data.append(trace_info)

                        if not loaded_trace_data:
                            st.error("No trace data available for plotting. This may indicate an issue with the analysis step.")
                            logging.error("No loaded_trace_data available for individual plots")
                            return

                        # Ensure we have matching numbers of trace_info and descriptors
                        min_length = min(len(loaded_trace_data), len(calculated_descriptors))
                        if len(loaded_trace_data) != len(calculated_descriptors):
                            logging.warning(f"Mismatch between loaded_trace_data ({len(loaded_trace_data)}) and calculated_descriptors ({len(calculated_descriptors)}). Using first {min_length} items.")

                        successful_plots = 0
                        for i in range(min_length):
                            trace_info = loaded_trace_data[i]
                            descriptor = calculated_descriptors[i]

                            # Skip if descriptor has error
                            if 'error' in descriptor:
                                st.warning(f"Skipping plot for {descriptor.get('well_marker_name', 'Unknown')} - {descriptor.get('error', 'Unknown error')}")
                                continue

                            if not descriptor:
                                logging.warning(f"Skipping plot for trace {trace_info.get('trace_idx', i)} - missing descriptor.")
                                continue

                            # Ensure 'data' key exists in trace_info for trace_sample
                            if 'trace_sample' not in trace_info:
                                # Try to get it from descriptor
                                if 'data' in descriptor:
                                    trace_info['trace_sample'] = descriptor['data']
                                    logging.info(f"Using trace data from descriptor for trace {trace_info.get('trace_idx', i)}")
                                else:
                                    logging.warning(f"Skipping plot for trace {trace_info.get('trace_idx', i)} - missing 'trace_sample' and 'data'.")
                                    continue

                            try:
                                time_vector = np.arange(len(trace_info['trace_sample'])) * st.session_state.dt

                                # Calculate WOSS if needed and selected
                                if 'WOSS' in selected_outputs and 'WOSS' not in descriptor:
                                    descriptor['WOSS'] = calculate_woss(descriptor, plot_settings_copy)

                                # Try to create the plot
                                try:
                                    fig_individual = plot_spectral_descriptors(
                                        trace_data=trace_info['trace_sample'],
                                        time_vector=time_vector,
                                        descriptors=descriptor,
                                        plot_settings=plot_settings_copy,
                                        trace_idx=trace_info.get('trace_idx'),
                                        well_marker_name=trace_info.get('well_marker_name')
                                    )
                                    st.plotly_chart(fig_individual, use_container_width=True)
                                    successful_plots += 1
                                except Exception as viz_error:
                                    # If visualization fails, try a simple fallback plot
                                    logging.error(f"Visualization function failed for {trace_info.get('well_marker_name', 'Unknown')}: {viz_error}")
                                    st.error(f"Visualization failed for {trace_info.get('well_marker_name', 'Unknown')}: {viz_error}")

                                    # Create a simple fallback plot showing just the trace data
                                    try:
                                        import plotly.graph_objects as go
                                        fig_fallback = go.Figure()
                                        fig_fallback.add_trace(go.Scatter(
                                            x=time_vector,
                                            y=trace_info['trace_sample'],
                                            mode='lines',
                                            name=f"Trace Data - {trace_info.get('well_marker_name', 'Unknown')}"
                                        ))
                                        fig_fallback.update_layout(
                                            title=f"Fallback Plot - {trace_info.get('well_marker_name', 'Unknown')}",
                                            xaxis_title="Time (s)",
                                            yaxis_title="Amplitude"
                                        )
                                        st.plotly_chart(fig_fallback, use_container_width=True)
                                        st.info("Showing fallback plot with trace data only due to visualization error.")
                                        successful_plots += 1
                                    except Exception as fallback_error:
                                        logging.error(f"Even fallback plot failed: {fallback_error}")
                                        st.error(f"Both main and fallback visualization failed: {fallback_error}")
                            except Exception as plot_error:
                                st.error(f"Error plotting {trace_info.get('well_marker_name', 'Unknown')}: {plot_error}")
                                logging.error(f"Individual plot error for trace {trace_info.get('trace_idx', i)}: {plot_error}", exc_info=True)

                        if successful_plots == 0:
                            st.error("No plots could be generated. Check the error messages above for details.")
                        else:
                            st.success(f"Successfully generated {successful_plots} individual plots.")

                    elif st.session_state.plot_mode_wells == 2: # Comparative Plots
                        num_traces = len(st.session_state.loaded_trace_data)
                        if num_traces == 0:
                            st.warning("No trace data loaded for comparative plot.")
                        else:
                            first_trace_sample = st.session_state.loaded_trace_data[0]['trace_sample']
                            time_vector = np.arange(len(first_trace_sample)) * st.session_state.dt

                            for output_name_to_plot in selected_outputs:
                                trace_names_for_plot = [trace.get('well_marker_name', f"Trace {trace.get('trace_idx', i)}") for i, trace in enumerate(st.session_state.loaded_trace_data)]
                                fig_comparative = make_subplots(rows=1, cols=num_traces, subplot_titles=trace_names_for_plot, shared_yaxes=True)

                                global_vmin, global_vmax = None, None # For shared colorbars on spectrograms
                                if output_name_to_plot in ["Magnitude Spectrogram", "Magnitude * Voice"]:
                                     # Calculate global min/max from the data if not in plot_settings
                                    all_spec_data = []
                                    desc_key_for_spec = "mag" if output_name_to_plot == "Magnitude Spectrogram" else "mag_voice"
                                    for desc_comp in calculated_descriptors:
                                        if desc_comp and desc_key_for_spec in desc_comp and isinstance(desc_comp[desc_key_for_spec], np.ndarray):
                                            all_spec_data.append(desc_comp[desc_key_for_spec])
                                    if all_spec_data:
                                        stacked_data = np.concatenate([s.flatten() for s in all_spec_data if s.size > 0])
                                        if stacked_data.size > 0:
                                            global_vmin = np.percentile(stacked_data, 1)
                                            global_vmax = np.percentile(stacked_data, 99)

                                for i, (trace_info, descriptor) in enumerate(zip(st.session_state.loaded_trace_data, calculated_descriptors)):
                                    if not descriptor: continue
                                    if 'trace_sample' not in trace_info: continue

                                    if output_name_to_plot == 'WOSS' and 'WOSS' not in descriptor:
                                        descriptor['WOSS'] = calculate_woss(descriptor, plot_settings_copy)

                                    add_output_to_subplot(
                                        fig=fig_comparative,
                                        output=output_name_to_plot,
                                        trace_data_item=trace_info,
                                        descriptors=descriptor,
                                        time_vector=time_vector,
                                        row=1, col=i + 1,
                                        plot_settings=plot_settings_copy,
                                        hfc_p95=plot_settings_copy['hfc_p95'],
                                        spec_decrease_p95=plot_settings_copy['spec_decrease_p95'],
                                        global_vmin=global_vmin,
                                        global_vmax=global_vmax,
                                        use_shared_colorbar=(output_name_to_plot in ["Magnitude Spectrogram", "Magnitude * Voice"]),
                                        is_last_subplot=(i == num_traces - 1)
                                    )

                                fig_comparative.update_layout(title_text=f"{output_name_to_plot} for Selected Well Markers", height=850, showlegend=(output_name_to_plot == "Normalized dominant frequencies"))
                                st.plotly_chart(fig_comparative, use_container_width=True)

                # Placeholder for other selection modes (Inline, Crossline, Polyline)
                elif st.session_state.selection_mode in ["Single inline (all crosslines)", "Single crossline (all inlines)", "By Polyline File Import"]:
                    st.info(f"Plotting for {st.session_state.selection_mode} mode.")
                    # This uses plot_multi_trace_section
                    if st.session_state.loaded_trace_data and calculated_descriptors:
                        time_vector = np.arange(len(st.session_state.loaded_trace_data[0]['trace_sample'])) * st.session_state.dt
                        trace_data_list_plot = [trace['trace_sample'] for trace in st.session_state.loaded_trace_data]

                        plot_settings_copy = st.session_state.plot_settings.copy()

                        # Ensure percentile cutoff values are available in plot_settings_copy

                        # Ensure HFC percentile cutoff value is available
                        if 'hfc_pc' not in plot_settings_copy and 'hfc_p95' not in plot_settings_copy:
                            # Fallback to default if no percentile cutoff value is available
                            plot_settings_copy['hfc_pc'] = 1.0
                            logging.warning("Export section: No HFC percentile cutoff value found, using fallback value: 1.0")
                        elif 'hfc_pc' not in plot_settings_copy:
                            # Use old naming for backward compatibility
                            plot_settings_copy['hfc_pc'] = plot_settings_copy.get('hfc_p95', 1.0)
                            logging.info(f"Export section: Using HFC cutoff value from hfc_p95: {plot_settings_copy['hfc_pc']}")

                        # Ensure Spectral Decrease percentile cutoff value is available
                        if 'spec_decrease_pc' not in plot_settings_copy and 'spec_decrease_p95' not in plot_settings_copy:
                            # Fallback to default if no percentile cutoff value is available
                            plot_settings_copy['spec_decrease_pc'] = 1.0
                            logging.warning("Export section: No Spectral Decrease percentile cutoff value found, using fallback value: 1.0")
                        elif 'spec_decrease_pc' not in plot_settings_copy:
                            # Use old naming for backward compatibility
                            plot_settings_copy['spec_decrease_pc'] = plot_settings_copy.get('spec_decrease_p95', 1.0)
                            logging.info(f"Export section: Using Spectral Decrease cutoff value from spec_decrease_p95: {plot_settings_copy['spec_decrease_pc']}")

                        for output_to_plot in selected_outputs:
                            title_str = f"{output_to_plot} Section"
                            x_axis_vals = None
                            x_axis_lbl = "Trace Index"

                            if st.session_state.selection_mode == "Single inline (all crosslines)":
                                # Check if calculated_descriptors is a dictionary (new implementation) or a list (existing implementation)
                                if isinstance(calculated_descriptors, dict):
                                    # Handle the case where calculated_descriptors is a dictionary (new implementation)
                                    # This section will be handled separately outside the loop
                                    continue
                                else:
                                    # Handle the case where calculated_descriptors is a list (existing implementation)
                                    title_str += f" - Inline {st.session_state.selected_inline_number if 'selected_inline_number' in st.session_state else st.session_state.selected_inline}"
                                    x_axis_vals = [st.session_state.header_loader.crosslines[np.where(st.session_state.header_loader.unique_indices == t['trace_idx'])[0][0]] for t in st.session_state.loaded_trace_data]
                                    x_axis_lbl = "Crossline"
                            elif st.session_state.selection_mode == "Single crossline (all inlines)":
                                title_str += f" - Crossline {st.session_state.selected_crossline_number}"
                                x_axis_vals = [st.session_state.header_loader.inlines[np.where(st.session_state.header_loader.unique_indices == t['trace_idx'])[0][0]] for t in st.session_state.loaded_trace_data]
                                x_axis_lbl = "Inline"
                            elif st.session_state.selection_mode == "By Polyline File Import":
                                title_str += " - Polyline Section"
                                x_axis_vals = np.arange(len(st.session_state.loaded_trace_data))


                            fig_section = plot_multi_trace_section(
                                trace_data_list_plot,
                                time_vector,
                                calculated_descriptors,
                                plot_settings_copy,
                                output_type=output_to_plot,
                                title=title_str,
                                x_axis_values=x_axis_vals,
                                x_axis_title=x_axis_lbl
                            )
                            if fig_section:
                                st.plotly_chart(fig_section, use_container_width=True)
                            else:
                                st.warning(f"Could not generate plot for {output_to_plot}")
                    else:
                        st.warning("No data available to plot for the selected section mode.")

                # Handle the case where calculated_descriptors is a dictionary (new implementation for Single inline mode)
                if isinstance(calculated_descriptors, dict) and st.session_state.selection_mode == "Single inline (all crosslines)":
                    # Get the selected inline number from session state
                    selected_inline = st.session_state.selected_inline_number if 'selected_inline_number' in st.session_state else st.session_state.selected_inline
                    st.subheader(f"Inline {selected_inline} Section")

                    # Allow the user to select which descriptors to display
                    available_outputs = list(calculated_descriptors.keys())
                    selected_output = st.selectbox("Select descriptor to display:", available_outputs)

                    # Get the descriptor data
                    descriptor_data = calculated_descriptors[selected_output]

                    # Get crossline numbers for x-axis
                    crossline_numbers = None
                    if st.session_state.header_loader:
                        # Create a mask for the selected inline
                        inline_mask = st.session_state.header_loader.inlines == selected_inline
                        # Get the corresponding crossline numbers
                        crossline_numbers = st.session_state.header_loader.crosslines[inline_mask]

                    # Create the section plot using the new plot_section_2d function
                    fig = plot_section_2d(
                        section_data=descriptor_data,
                        time_vector=np.arange(descriptor_data.shape[1]) * st.session_state.dt,
                        plot_settings=st.session_state.plot_settings,
                        output_type=selected_output,
                        title=f"{selected_output} - Inline {selected_inline}",
                        x_axis_values=crossline_numbers,  # Use crossline numbers for x-axis
                        x_axis_title="Crossline"
                    )

                    # Display the plot
                    st.plotly_chart(fig, use_container_width=True)

                # Handle the case where calculated_descriptors is a dictionary (new implementation for Single crossline mode)
                elif isinstance(calculated_descriptors, dict) and st.session_state.selection_mode == "Single crossline (all inlines)":
                    # Get the selected crossline number from session state
                    selected_crossline = st.session_state.selected_crossline_number if 'selected_crossline_number' in st.session_state else st.session_state.selected_crossline
                    st.subheader(f"Crossline {selected_crossline} Section")

                    # Allow the user to select which descriptors to display
                    available_outputs = list(calculated_descriptors.keys())
                    selected_output = st.selectbox("Select descriptor to display:", available_outputs, key="crossline_output_selector")

                    # Get the descriptor data
                    descriptor_data = calculated_descriptors[selected_output]

                    # Get inline numbers for x-axis
                    inline_numbers = None
                    if st.session_state.header_loader:
                        # Create a mask for the selected crossline
                        crossline_mask = st.session_state.header_loader.crosslines == selected_crossline
                        # Get the corresponding inline numbers
                        inline_numbers = st.session_state.header_loader.inlines[crossline_mask]

                    # Create the section plot using the new plot_section_2d function
                    fig = plot_section_2d(
                        section_data=descriptor_data,
                        time_vector=np.arange(descriptor_data.shape[1]) * st.session_state.dt,
                        plot_settings=st.session_state.plot_settings,
                        output_type=selected_output,
                        title=f"{selected_output} - Crossline {selected_crossline}",
                        x_axis_values=inline_numbers,  # Use inline numbers for x-axis
                        x_axis_title="Inline"
                    )

                    # Display the plot
                    st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.error(f"An error occurred during plot generation: {e}")
            logging.error(f"Plot generation failed in render_view_results: {e}", exc_info=True)

    # Display statistics if available
    if st.session_state.get('descriptor_statistics'):
        with st.expander("📊 Descriptor Statistics Summary", expanded=True):
            st.markdown("### Signal Descriptor Statistics")
            st.markdown("The following statistics have been calculated for the selected traces:")

            # Group descriptors into categories for better organization
            categories = {
                "Primary Descriptors": [
                    "Slope Magnitude", "Normalized HFC", "Normalized Dominant Frequencies",
                    "WOSS", "Normalized Dominant Frequency", "Mag*Voice Slope"
                ],
                "Spectral Properties": [
                    "Spectral Slope", "Spectral Bandwidth", "Spectral Rolloff",
                    "Spectral Decrease", "Magnitude * Voice"
                ],
                "Signal Characteristics": [
                    "Input Signal", "HFC", "Magnitude Spectrogram"
                ],
                "Other Descriptors": []  # For any remaining descriptors
            }

            # Get the statistics
            stats = st.session_state.descriptor_statistics

            # Assign each descriptor to a category
            categorized_stats = {cat: [] for cat in categories.keys()}

            for desc_name, desc_stats in stats.items():
                if not isinstance(desc_stats, dict):
                    continue  # Skip if not a dict
                # Now we know desc_stats is a dict
                if not desc_stats.get('values', []):  # Skip empty descriptors
                    continue

                # Find which category this descriptor belongs to
                assigned = False
                for cat_name, cat_descriptors in categories.items():
                    if desc_name in cat_descriptors:
                        if isinstance(desc_stats.get('min'), (int, float)):
                            categorized_stats[cat_name].append({
                                "Descriptor": desc_name,
                                "Min": desc_stats.get('min'),  # Keep as numeric
                                "Max": desc_stats.get('max'),  # Keep as numeric
                                "P5": desc_stats.get('p5'),    # Keep as numeric
                                "P90": desc_stats.get('p90')   # Keep as numeric
                            })
                        else:
                            categorized_stats[cat_name].append({
                                "Descriptor": desc_name,
                                "Min": desc_stats.get('min'),
                                "Max": desc_stats.get('max'),
                                "P5": desc_stats.get('p5'),
                                "P90": desc_stats.get('p90')
                            })
                        assigned = True
                        break

                # If not assigned to any specific category, put in "Other Descriptors"
                if not assigned and desc_stats.get('values', []):
                    if isinstance(desc_stats.get('min'), (int, float)):
                        categorized_stats["Other Descriptors"].append({
                            "Descriptor": desc_name,
                            "Min": desc_stats.get('min'),  # Keep as numeric
                            "Max": desc_stats.get('max'),  # Keep as numeric
                            "P5": desc_stats.get('p5'),    # Keep as numeric
                            "P90": desc_stats.get('p90')   # Keep as numeric
                        })
                    else:
                        categorized_stats["Other Descriptors"].append({
                            "Descriptor": desc_name,
                            "Min": desc_stats.get('min'),
                            "Max": desc_stats.get('max'),
                            "P5": desc_stats.get('p5'),
                            "P90": desc_stats.get('p90')
                        })

            # Display tables by category
            for cat_name, cat_stats in categorized_stats.items():
                if cat_stats:  # Only show categories with data
                    st.subheader(cat_name)
                    # Create DataFrame with numeric values
                    df = pd.DataFrame(cat_stats)

                    # Format the DataFrame for display with 4 decimal places
                    # but keep the underlying data as numeric
                    formatted_df = df.style.format({
                        "Min": "{:.4f}",
                        "Max": "{:.4f}",
                        "P5": "{:.4f}",
                        "P90": "{:.4f}"
                    }, na_rep="N/A")

                    st.table(formatted_df)

            # Add explanation
            st.markdown("""
            **Note:**
            - **Min/Max**: Absolute minimum and maximum values across all traces
            - **P5**: 5th percentile value (95% of values are above this)
            - **P90**: 90th percentile value (10% of values are above this)
            """)

    # Add buttons for navigation
    st.markdown("---")
    col1, col2 = st.columns(2)

    with col1:
        if st.button("Back to Analysis", key="back_to_analysis_button"):
            st.session_state.current_step = "analyze_data"
            st.rerun()

    with col2:
        if st.button("Configure Export", key="configure_export_button"):
            st.session_state.current_step = "configure_export"
            st.rerun()

    # Add a "Start New Analysis" button to the sidebar
    st.sidebar.markdown("---")
    if st.sidebar.button("🔄 Start New Analysis", use_container_width=True, key="view_results_new_analysis"):
        reset_state()
        st.success("Starting new analysis. All temporary data has been cleared.")
        st.rerun()

def render_download_export():
    """Render the download export UI."""
    st.header("Step 5: Download Exported Files")

    export_dir = st.session_state.get('export_output_dir')
    exported_files = st.session_state.get('exported_files_info')

    if not export_dir or not exported_files:
        st.warning("No exported files found or export directory missing. Please go back and complete the export process.")
        if st.button("Go to Data Loading"):
            reset_state()
            st.rerun()
    else:
        st.success("Export process completed successfully!")
        st.write("The following files are ready for download:")
        st.info(f"Files are temporarily stored and will be cleaned up after starting a new analysis or closing the session.")

        df_files = pd.DataFrame(exported_files)
        df_files['filename'] = df_files['path'].apply(os.path.basename)
        st.dataframe(df_files[['attribute', 'filename']])

        # Zip file download
        try:
            zip_buffer = BytesIO()
            # Use a base filename derived from the original SEG-Y if possible
            base_zip_filename = "WOSS_AOI_Export_Files.zip"
            if 'segy_file_info' in st.session_state and st.session_state.segy_file_info:
                base_zip_filename = f"{st.session_state.segy_file_info['name']}_AOI_Export_Files.zip"

            with st.spinner("Preparing zip file for download..."):
                with zipfile.ZipFile(zip_buffer, "a", zipfile.ZIP_DEFLATED, False) as zip_file:
                    for file_info in exported_files:
                        try:
                            file_path = file_info['path']
                            if os.path.exists(file_path):
                                arcname = os.path.basename(file_path)  # Name inside the zip file
                                zip_file.write(file_path, arcname=arcname)
                                logging.info(f"Added {arcname} to zip.")
                            else:
                                st.warning(f"File not found, cannot add to zip: {file_path}")
                                logging.warning(f"File not found for zipping: {file_path}")
                        except Exception as ze:
                            st.warning(f"Error adding {os.path.basename(file_info.get('path','N/A'))} to zip: {ze}")
                            logging.error(f"Error adding file to zip: {ze}", exc_info=True)

            zip_buffer.seek(0)  # Rewind buffer
            st.download_button(
                label="Download All Exported Files (.zip)",
                data=zip_buffer,
                file_name=base_zip_filename,
                mime="application/zip",
                key="download_zip_button"
            )
            st.success("Zip file prepared. Click the button above to download.")

        except Exception as e:
            st.error(f"Could not create zip file: {e}")
            logging.error(f"Zip file creation failed: {e}", exc_info=True)

        st.markdown("---")
        col1, col2 = st.columns(2)

        with col1:
            if st.button("Back to Mode Selection"):
                # Keep the export directory for now in case user wants to come back
                st.session_state.current_step = "select_mode"
                st.rerun()

        with col2:
            if st.button("Start New Analysis"):
                # Clean up the persistent temporary export directory
                if export_dir and os.path.exists(export_dir):
                    try:
                        shutil.rmtree(export_dir)
                        logging.info(f"Cleaned up export directory: {export_dir}")
                    except Exception as clean_e:
                        logging.warning(f"Could not remove export directory {export_dir}: {clean_e}")

                reset_state()  # Reset session state for a new run
                st.rerun()

    # Add a "Start New Analysis" button to the sidebar
    st.sidebar.markdown("---")
    if st.sidebar.button("🔄 Start New Analysis", use_container_width=True):
        reset_state()
        st.success("Starting new analysis. All temporary data has been cleared.")
        st.rerun()
