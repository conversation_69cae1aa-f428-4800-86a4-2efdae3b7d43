"""
Select Area Page for the WOSS Seismic Analysis Tool.

This module handles the UI rendering for selecting the area of interest.
It follows the principles outlined in rules.md, particularly regarding
the separation of concerns between UI and backend logic.
"""

import streamlit as st
import numpy as np
import pandas as pd
import logging

# Import common modules
from common.constants import APP_TITLE
from common.session_state import initialize_session_state, reset_state
from common.ui_elements import get_suggested_batch_size

# Import utility functions
from utils import data_utils
from utils.data_utils import get_surfaces, get_well_marker_pairs # Corrected imports
from utils.data_utils import get_nearest_trace_index
from utils.general_utils import parse_polyline_string, find_traces_near_polyline

# Import GPU functions if available
try:
    from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu_2d_chunked
    GPU_FUNCTIONS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Could not import GPU functions: {e}")
    GPU_FUNCTIONS_AVAILABLE = False
    # Define dummy function
    def dlogst_spec_descriptor_gpu_2d_chunked(*args, **kwargs):
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu_2d_chunked not available.")

def render(trace_selection=False):
    """Render the select area page UI."""
    initialize_session_state()

    if not trace_selection:
        st.header("Step 3: Select Analysis Mode")
        st.sidebar.header("Mode Selection")

        # Check if data is loaded
        if not st.session_state.header_loader:
            st.warning("Please load data first.")
            st.session_state.current_step = "load_data"
            st.rerun()
            return

        # Mode Selection with a unique key
        mode_options = [
            "By well markers",
            "Single inline (all crosslines)",
            "Single crossline (all inlines)",
            "By inline/crossline section (AOI)",
            "By Polyline File Import"
        ]
        # Initialize mode_selector if not already in session state
        if 'mode_selector' not in st.session_state:
            st.session_state.mode_selector = st.session_state.selection_mode or mode_options[0]
        # Determine the index for the selectbox
        mode_index = mode_options.index(st.session_state.mode_selector) if st.session_state.mode_selector in mode_options else 0
        # Create the selectbox without on_change callback
        selected_mode = st.sidebar.selectbox(
            "Select Mode",
            options=mode_options,
            index=mode_index,
            key="mode_selector"
        )
        # Update the selection_mode in session state
        st.session_state.selection_mode = selected_mode

        # AOI Mode
        if st.session_state.selection_mode == "By inline/crossline section (AOI)":
            st.subheader("AOI Mode")
            
            # Get actual inline/crossline ranges from the loaded SEG-Y file
            if st.session_state.header_loader:
                # Get the full inline/crossline range
                range_dict = st.session_state.header_loader.get_inline_crossline_range()
                actual_inline_min = range_dict['inline_min']
                actual_inline_max = range_dict['inline_max']
                actual_xline_min = range_dict['xline_min']
                actual_xline_max = range_dict['xline_max']
                
                # Initialize AOI bounds if not set yet
                if not hasattr(st.session_state, 'aoi_inline_min') or st.session_state.aoi_inline_min is None:
                    st.session_state.aoi_inline_min = actual_inline_min
                if not hasattr(st.session_state, 'aoi_inline_max') or st.session_state.aoi_inline_max is None:
                    st.session_state.aoi_inline_max = actual_inline_max
                if not hasattr(st.session_state, 'aoi_xline_min') or st.session_state.aoi_xline_min is None:
                    st.session_state.aoi_xline_min = actual_xline_min
                if not hasattr(st.session_state, 'aoi_xline_max') or st.session_state.aoi_xline_max is None:
                    st.session_state.aoi_xline_max = actual_xline_max
                
                # Display AOI bounds input
                st.info(f"Inline range: {actual_inline_min} - {actual_inline_max}, Crossline range: {actual_xline_min} - {actual_xline_max}")
                col1, col2 = st.columns(2)
                with col1:
                    st.session_state.aoi_inline_min = st.number_input(
                        "Inline Min",
                        min_value=actual_inline_min,
                        max_value=actual_inline_max,
                        value=st.session_state.aoi_inline_min,
                        step=1,
                        key="aoi_inline_min_input"
                    )
                    st.session_state.aoi_xline_min = st.number_input(
                        "Crossline Min",
                        min_value=actual_xline_min,
                        max_value=actual_xline_max,
                        value=st.session_state.aoi_xline_min,
                        step=1,
                        key="aoi_xline_min_input"
                    )
                
                with col2:
                    st.session_state.aoi_inline_max = st.number_input(
                        "Inline Max",
                        min_value=actual_inline_min,
                        max_value=actual_inline_max,
                        value=st.session_state.aoi_inline_max,
                        step=1,
                        key="aoi_inline_max_input"
                    )
                    st.session_state.aoi_xline_max = st.number_input(
                        "Crossline Max",
                        min_value=actual_xline_min,
                        max_value=actual_xline_max,
                        value=st.session_state.aoi_xline_max,
                        step=1,
                        key="aoi_xline_max_input"
                    )
                
                # Processing option - only Full AOI is supported
                st.session_state.aoi_processing_option = "Full AOI"  # Set directly to Full AOI
                st.info("Processing Option: Full AOI")
                
                # Store the AOI bounds for reference
                st.session_state.aoi_bounds = {
                    'inline_min': st.session_state.aoi_inline_min,
                    'inline_max': st.session_state.aoi_inline_max,
                    'xline_min': st.session_state.aoi_xline_min,
                    'xline_max': st.session_state.aoi_xline_max
                }
                
                # Create the header DataFrame
                headers_df = pd.DataFrame({
                    'inline': st.session_state.header_loader.inlines,
                    'crossline': st.session_state.header_loader.crosslines,
                    'trace_idx': st.session_state.header_loader.unique_indices
                })
                
                # Add a proceed button to find traces within the AOI
                if st.button("Proceed", key="aoi_proceed_button"):
                    # Filter by inline/crossline range
                    aoi_df = headers_df[
                        (headers_df['inline'] >= st.session_state.aoi_inline_min) & 
                        (headers_df['inline'] <= st.session_state.aoi_inline_max) & 
                        (headers_df['crossline'] >= st.session_state.aoi_xline_min) & 
                        (headers_df['crossline'] <= st.session_state.aoi_xline_max)
                    ]
                    
                    if aoi_df.empty:
                        st.error("No traces found within the specified AOI.")
                    else:
                        # Store selected traces
                        st.session_state.selected_indices = aoi_df['trace_idx'].tolist()
                        st.session_state.area_selected = True
                        st.session_state.area_selected_mode = 'aoi'
                        
                        # Store selected data
                        st.session_state.selected_data_for_precompute = {
                            "mode": "aoi",
                            "aoi_bounds": st.session_state.aoi_bounds,
                            "selected_indices": st.session_state.selected_indices
                        }
                        
                        # Transition directly to analyze data for AOI export
                        st.success(f"Found {len(aoi_df)} traces within the AOI. Proceeding to AOI Export Configuration.")
                        st.session_state.current_step = "analyze_data"
                        st.rerun()
            else:
                st.warning("SEG-Y file not properly loaded. Please reload in Step 1.")
        
        # Well Markers Mode
        elif st.session_state.selection_mode == "By well markers":
            st.subheader("Well Markers Mode")
            # Display HFC percentile value from Step 2
            hfc_val = st.session_state.get('hfc_p95')
            if hfc_val is not None:
                st.info(f"Using HFC percentile value from Step 2: {hfc_val:.3f}")
            else:
                st.warning("HFC percentile value not configured. Please complete Step 2.")
            if st.session_state.well_df is not None and not st.session_state.well_df.empty:
                # Use the correct function to get available markers
                available_markers = get_surfaces(st.session_state.well_df) # Corrected function call
                st.session_state.selected_well_markers = st.multiselect(
                    "Select Well Markers",
                    options=available_markers,
                    default=available_markers[:min(len(available_markers), 3)]  # Default to first 3 or fewer
                )

                # --- Add well-marker pair dropdown ---
                # Get all well-marker pairs
                well_marker_dict = get_well_marker_pairs(st.session_state.well_df)
                # Filter pairs by selected markers
                filtered_pairs = [pair for pair in well_marker_dict.keys()
                                  if pair.split(" - ")[1] in st.session_state.selected_well_markers]
                if filtered_pairs:
                    # Default to selecting the first pair if none are selected yet, or if the previous single selection is in the list
                    default_selection = []
                    if st.session_state.get('selected_well_marker_pairs'):
                        # If it was a single string before, make it a list
                        current_selection = st.session_state.selected_well_marker_pairs
                        if isinstance(current_selection, str):
                            current_selection = [current_selection]
                        # Keep valid previous selections that are in the filtered_pairs
                        default_selection = [s for s in current_selection if s in filtered_pairs]

                    if not default_selection and filtered_pairs: # If still empty and pairs exist, pick the first one
                        default_selection = [filtered_pairs[0]]

                    st.session_state.selected_well_marker_pairs = st.multiselect(
                        "Select Well-Marker Pair(s) for Analysis", # Changed label to match reference
                        options=filtered_pairs,
                        default=default_selection, # Ensure default is a list
                        key="well_marker_pair_multiselect" # New key might be good
                    )

                    # Plot Mode selection (matching reference implementation)
                    st.session_state.plot_mode_wells = st.radio(
                        "Plot Mode",
                        options=[1, 2],
                        format_func=lambda x: "Individual Plots" if x == 1 else "Comparative Plots",
                        index=0,  # Default to Individual Plots
                        key="plot_mode_wells_radio"
                    )

                    # Map plot_mode_wells to well_analysis_sub_option for compatibility
                    if st.session_state.plot_mode_wells == 1:
                        st.session_state.well_analysis_sub_option = "Plot Individual Wells Analysis"
                    else:
                        st.session_state.well_analysis_sub_option = "Grouping Well Analysis"

                else:
                    st.session_state.selected_well_marker_pairs = None
                    st.info("No well-marker pairs available for the selected markers.")

                # The plot_twt checkbox remains.
                st.session_state.plot_twt = st.checkbox("Plot Two-Way Travel Time (TWT)", value=False)
            else:
                st.warning("No well data available. Please upload well data in Step 1.")

        # Single Inline Mode
        elif st.session_state.selection_mode == "Single inline (all crosslines)":
            st.subheader("Single Inline Mode")
            if st.session_state.header_loader:
                import numpy as np
                # Get min and max inline numbers
                min_inline = int(np.min(st.session_state.header_loader.inlines))
                max_inline = int(np.max(st.session_state.header_loader.inlines))

                # Default to previously selected inline or min inline
                default_inline = st.session_state.selected_inline if st.session_state.selected_inline else min_inline

                # Inline number selection
                st.session_state.selected_inline = st.number_input(
                    f"Specify an inline number ({min_inline}-{max_inline}):",
                    min_value=min_inline,
                    max_value=max_inline,
                    value=default_inline,
                    step=1
                )

                # Batch size selection if GPU is available
                if st.session_state.get('GPU_AVAILABLE', False):
                    suggested_batch, free_mb = get_suggested_batch_size()
                    st.info(f"Estimated free GPU memory: {free_mb:.1f} MB. Suggested batch size: {suggested_batch}")
                    st.session_state.batch_size = st.number_input(
                        "Batch size for GPU processing:",
                        min_value=10,
                        max_value=4000,
                        value=suggested_batch,
                        step=10,
                        help="Number of traces to process at once. Higher values use more GPU memory."
                    )
                else:
                    st.warning("GPU processing not available. Processing will be slower.")
                    st.session_state.batch_size = None

                # Add a button to load traces for the selected inline
                if st.button("Load Traces for Inline"):
                    with st.spinner(f"Loading traces for inline {st.session_state.selected_inline}..."):
                        try:
                            # Get all trace indices for the selected inline
                            inline_mask = st.session_state.header_loader.inlines == st.session_state.selected_inline
                            chosen_indices = st.session_state.header_loader.unique_indices[inline_mask]
                            
                            if len(chosen_indices) == 0:
                                st.error(f"No traces found for inline {st.session_state.selected_inline}")
                                st.session_state.loaded_trace_data = []
                                st.session_state.traces_loaded = False
                            else:
                                # Load trace data for the selected inline
                                loaded_data = []
                                for idx in chosen_indices:
                                    try:
                                        trace_sample = data_utils.load_trace_sample(
                                            st.session_state.header_loader.source_file_path,
                                            idx
                                        )
                                        loaded_data.append({
                                            'trace_sample': trace_sample,
                                            'trace_idx': idx,
                                            'inline': st.session_state.selected_inline,
                                            'crossline': st.session_state.header_loader.crosslines[idx]
                                        })
                                    except Exception as e:
                                        st.error(f"Error loading trace {idx}: {e}")
                                
                                st.session_state.loaded_trace_data = loaded_data
                                st.session_state.traces_loaded = True
                                st.session_state.selected_indices = chosen_indices.tolist()
                                st.success(f"Successfully loaded {len(loaded_data)} traces for inline {st.session_state.selected_inline}")
                                
                                # Set flag to show trace selection UI
                                st.session_state.show_trace_selection = True
                                
                        except Exception as e:
                            st.error(f"Error loading traces: {e}")
                            logging.error(f"Error loading traces: {e}", exc_info=True)
                
                # Show trace selection and processing options if traces are loaded
                if st.session_state.get('traces_loaded', False) and st.session_state.loaded_trace_data:
                    st.subheader("Trace Selection and Processing")
                    
                    # Show number of loaded traces
                    st.info(f"{len(st.session_state.loaded_trace_data)} traces loaded for inline {st.session_state.selected_inline}")
                    
                    # Allow selection of outputs to calculate
                    available_outputs = [
                        "Input Signal",
                        "Normalized dominant frequencies",
                        "Spectral Slope",
                        "Spectral Bandwidth",
                        "Spectral Rolloff",
                        "Mag*Voice Slope",
                        "Spectral Decrease",
                        "HFC",
                        "WOSS"
                    ]
                    
                    st.session_state.selected_outputs = st.multiselect(
                        "Select outputs to calculate:",
                        options=available_outputs,
                        default=["WOSS", "Spectral Slope", "Spectral Decrease"],
                        key="outputs_multiselect"
                    )
                    
                    # Add a button to process the selected traces
                    if st.button("Process Traces"):
                        if not st.session_state.selected_outputs:
                            st.warning("Please select at least one output to calculate.")
                        else:
                            with st.spinner("Processing traces..."):
                                try:
                                    # Prepare data for processing
                                    trace_data_list = [item['trace_sample'] for item in st.session_state.loaded_trace_data]
                                    
                                    # Get spectral parameters from plot_settings (consistent with analyze_data_page.py)
                                    base_plot_settings = st.session_state.get('plot_settings', {})
                                    spectral_params = {
                                        'dt': st.session_state.get('dt', 0.004),
                                        'use_band_limited': base_plot_settings.get('use_band_limited', True),
                                        'shape': base_plot_settings.get('shape', 0.35),  # Fixed: was 'sine' (string), now numeric
                                        'kmax': base_plot_settings.get('kmax', 120.0),   # Fixed: consistent with other pages
                                        'int_val': base_plot_settings.get('int_val', 35.0),  # Fixed: consistent with other pages
                                        'b1': base_plot_settings.get('b1', 5.0),         # Fixed: consistent with other pages
                                        'b2': base_plot_settings.get('b2', 40.0),        # Fixed: consistent with other pages
                                        'p_bandwidth': base_plot_settings.get('p_bandwidth', 2.0),  # Fixed: consistent with other pages
                                        'roll_percent': base_plot_settings.get('roll_percent', 0.85),
                                        'epsilon': base_plot_settings.get('epsilon', 1e-4),
                                        'fdom_exponent': base_plot_settings.get('fdom_exponent', 2.0)
                                    }
                                    
                                    # Process using GPU if available
                                    if st.session_state.get('GPU_AVAILABLE', False) and st.session_state.batch_size:
                                        # Convert trace data to 2D array (samples x traces)
                                        trace_data_2d = np.stack(trace_data_list, axis=1)
                                        
                                        # Map display names to internal names
                                        output_mapping = {
                                            "Input Signal": "data",
                                            "Normalized dominant frequencies": "norm_fdom",
                                            "Spectral Slope": "spec_slope",
                                            "Spectral Bandwidth": "spec_bandwidth",
                                            "Spectral Rolloff": "spec_rolloff",
                                            "Mag*Voice Slope": "mag_voice_slope",
                                            "Spectral Decrease": "spec_decrease",
                                            "HFC": "hfc",
                                            "WOSS": "WOSS"
                                        }
                                        
                                        # Get internal names for selected outputs
                                        outputs_to_calculate = [output_mapping[out] for out in st.session_state.selected_outputs 
                                                              if out in output_mapping]
                                        
                                        # Ensure we have components for WOSS if it's selected
                                        if "WOSS" in st.session_state.selected_outputs:
                                            required_for_woss = ["hfc", "norm_fdom", "mag_voice_slope"]
                                            for comp in required_for_woss:
                                                if comp not in outputs_to_calculate:
                                                    outputs_to_calculate.append(comp)
                                        
                                        # Call the GPU function
                                        # Filter out params not used by dlogst_spec_descriptor_gpu, e.g., 'epsilon', 'fdom_exponent'
                                        filtered_params = {
                                            k: v for k, v in spectral_params.items() 
                                            if k not in ['dt', 'epsilon', 'fdom_exponent', 'hfc_p95']
                                        }
                                        all_descriptors = dlogst_spec_descriptor_gpu_2d_chunked(
                                            trace_data_2d,
                                            spectral_params['dt'],
                                            batch_size=st.session_state.batch_size,
                                            **filtered_params
                                        )

                                        # Filter to only the requested descriptors
                                        calculated_descriptors = {key: all_descriptors[key] for key in outputs_to_calculate
                                                                 if key in all_descriptors}
                                        
                                        # Store results in session state
                                        st.session_state.calculated_descriptors = calculated_descriptors
                                        st.session_state.analysis_complete = True
                                        
                                        # Show success message
                                        st.success("Processing completed successfully!")
                                        
                                        # Show visualization options
                                        st.subheader("Visualization")
                                        
                                        # Allow selection of output to visualize
                                        output_to_visualize = st.selectbox(
                                            "Select output to visualize:",
                                            options=st.session_state.selected_outputs,
                                            index=0
                                        )
                                        
                                        # Get the data for visualization
                                        if output_to_visualize in output_mapping:
                                            internal_name = output_mapping[output_to_visualize]
                                            if internal_name in st.session_state.calculated_descriptors:
                                                data = st.session_state.calculated_descriptors[internal_name]
                                                
                                                # Create a simple plot using Plotly
                                                import plotly.express as px
                                                
                                                # Convert to DataFrame for easier plotting
                                                df = pd.DataFrame(data)
                                                
                                                # Create a heatmap
                                                fig = px.imshow(
                                                    df,
                                                    aspect="auto",
                                                    labels=dict(x="Trace", y="Sample", color=output_to_visualize),
                                                    title=f"{output_to_visualize} for Inline {st.session_state.selected_inline}"
                                                )
                                                
                                                # Update layout
                                                fig.update_layout(
                                                    xaxis_title="Trace Number",
                                                    yaxis_title="Time Sample",
                                                    coloraxis_colorbar=dict(title=output_to_visualize)
                                                )
                                                
                                                # Show the plot
                                                st.plotly_chart(fig, use_container_width=True)
                                            
                                            # Add button to proceed to export
                                            if st.button("Proceed to Export"):
                                                st.session_state.current_step = "export_results"
                                                st.rerun()
                                        
                                    else:
                                        st.warning("GPU processing is required for this operation. Please ensure GPU is available and configured.")
                                        
                                except Exception as e:
                                    st.error(f"Error processing traces: {e}")
                                    logging.error(f"Error processing traces: {e}", exc_info=True)
            else:
                st.warning("SEG-Y headers not loaded yet.")

        # Single Crossline Mode
        elif st.session_state.selection_mode == "Single crossline (all inlines)":
            st.subheader("Single Crossline Mode")
            if st.session_state.header_loader:
                import numpy as np
                # Get min and max crossline numbers
                min_crossline = int(np.min(st.session_state.header_loader.crosslines))
                max_crossline = int(np.max(st.session_state.header_loader.crosslines))

                # Default to previously selected crossline or min crossline
                default_crossline = st.session_state.selected_crossline if st.session_state.selected_crossline else min_crossline

                # Crossline number selection
                st.session_state.selected_crossline = st.number_input(
                    f"Specify a crossline number ({min_crossline}-{max_crossline}):",
                    min_value=min_crossline,
                    max_value=max_crossline,
                    value=default_crossline,
                    step=1
                )

                # Batch size selection if GPU is available
                if st.session_state.get('GPU_AVAILABLE', False):
                    suggested_batch, free_mb = get_suggested_batch_size()
                    st.info(f"Estimated free GPU memory: {free_mb:.1f} MB. Suggested batch size: {suggested_batch}")
                    st.session_state.batch_size = st.number_input(
                        "Batch size for GPU processing:",
                        min_value=10,
                        max_value=4000,
                        value=suggested_batch,
                        step=10,
                        help="Number of traces to process at once. Higher values use more GPU memory."
                    )
                else:
                    st.warning("GPU processing not available. Processing will be slower.")
                    st.session_state.batch_size = None
            else:
                st.warning("SEG-Y headers not loaded yet.")

        # AOI Mode
        elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
            st.subheader("AOI Mode")

            # Get actual inline/crossline ranges from the loaded SEG-Y file
            if st.session_state.header_loader:
                # Extract actual inline/crossline ranges from the header loader
                inlines = st.session_state.header_loader.inlines
                crosslines = st.session_state.header_loader.crosslines

                # Get the full inline/crossline range using the new method
                range_dict = st.session_state.header_loader.get_inline_crossline_range()
                actual_inline_min = range_dict['inline_min']
                actual_inline_max = range_dict['inline_max']
                actual_xline_min = range_dict['xline_min']
                actual_xline_max = range_dict['xline_max']

                # Initialize session state values if not already set
                if st.session_state.aoi_inline_min is None:
                    st.session_state.aoi_inline_min = actual_inline_min
                if st.session_state.aoi_inline_max is None:
                    st.session_state.aoi_inline_max = actual_inline_max
                if st.session_state.aoi_xline_min is None:
                    st.session_state.aoi_xline_min = actual_xline_min
                if st.session_state.aoi_xline_max is None:
                    st.session_state.aoi_xline_max = actual_xline_max

                # No need to initialize fixed values for specific inline/crossline as we only support Full AOI
                st.session_state.aoi_plot_fixed_value = None

            # Display AOI bounds
            col1, col2 = st.columns(2)
            with col1:
                st.session_state.aoi_inline_min = st.number_input(
                    "Inline Min",
                    value=st.session_state.aoi_inline_min or 0,
                    step=1,
                    key="aoi_inline_min_input"
                )
                st.session_state.aoi_xline_min = st.number_input(
                    "Crossline Min",
                    value=st.session_state.aoi_xline_min or 0,
                    step=1,
                    key="aoi_xline_min_input"
                )

            with col2:
                st.session_state.aoi_inline_max = st.number_input(
                    "Inline Max",
                    value=st.session_state.aoi_inline_max or 100,
                    step=1,
                    key="aoi_inline_max_input"
                )
                st.session_state.aoi_xline_max = st.number_input(
                    "Crossline Max",
                    value=st.session_state.aoi_xline_max or 100,
                    step=1,
                    key="aoi_xline_max_input"
                )

            # Processing option selection - only Full AOI is available
            st.session_state.aoi_processing_option = "Full AOI"  # Set directly to Full AOI
            st.info("Processing Option: Full AOI")

            # No specific inline/crossline input needed as we only support Full AOI
            st.session_state.aoi_plot_section_type = None
            st.session_state.aoi_plot_fixed_value = None

            # Also ensure the headers dataframe is properly loaded and filtered
            if st.session_state.header_loader:
                headers_df = pd.DataFrame({
                    'inline': st.session_state.header_loader.inlines,
                    'crossline': st.session_state.header_loader.crosslines,
                    'x': st.session_state.header_loader.x_coords,
                    'y': st.session_state.header_loader.y_coords
                })
                headers_df['trace_idx'] = st.session_state.header_loader.unique_indices
            else:
                st.warning("SEG-Y headers not loaded yet.")
                headers_df = pd.DataFrame()

            # Store the filtered AOI bounds for reference
            st.session_state.aoi_bounds = {
                'inline_min': st.session_state.aoi_inline_min,
                'inline_max': st.session_state.aoi_inline_max,
                'xline_min': st.session_state.aoi_xline_min,
                'xline_max': st.session_state.aoi_xline_max
            }

        # Polyline Mode
        elif st.session_state.selection_mode == "By Polyline File Import":
            st.subheader("Polyline Mode")

            # Polyline File Upload
            st.subheader("Upload Polyline File")
            polyline_file = st.file_uploader("Choose a polyline file", type=["txt", "csv"], key="polyline_file_uploader")
            if polyline_file is not None:
                st.session_state.polyline_file_info = {
                    'name': polyline_file.name,
                    'buffer': polyline_file
                }
                st.success(f"Uploaded {polyline_file.name}")

                # Show tolerance slider only after file is uploaded
                st.session_state.polyline_tolerance = st.slider(
                    "Polyline Tolerance",
                    min_value=0.0,
                    max_value=20.0,  # Maximum value of 20.0
                    value=st.session_state.polyline_tolerance,
                    step=1.0,  # Changed from 0.1 to 1.0
                    key="polyline_tolerance_slider",
                    help="Maximum distance from polyline to include traces (larger values select more traces)"
                )
            else:
                st.warning("Please upload a polyline file before proceeding.")

        # Add a separator before the proceed button
        st.markdown("---")

        # Proceed Button with a more specific key and primary styling
        st.markdown("### Complete Step 3 and Continue")

        # Check if display_params_configured is set, if not, set it to True if stats_defaults is available
        if not st.session_state.get('display_params_configured') and st.session_state.get('stats_defaults') is not None:
            logging.info("Setting display_params_configured to True as stats_defaults is available")
            st.session_state.display_params_configured = True

        # Log current state for debugging
        logging.info(f"Before button click - display_params_configured: {st.session_state.get('display_params_configured')}")
        logging.info(f"Before button click - area_selected: {st.session_state.get('area_selected')}")

        if st.button("Next: Analyze Data", key="select_area_next_button", use_container_width=True, type="primary"):
            # Process selection based on mode
            if st.session_state.selection_mode == "By well markers":
                selected_labels = st.session_state.get('selected_well_marker_pairs', []) # Get the labels from the multiselect
                if not selected_labels:
                    st.warning("Please select at least one well-marker pair to continue.")
                    st.stop() # Stop execution to prevent transition

                selected_pairs_data = []
                all_selected_trace_indices = []
                well_df = st.session_state.get('well_df') # Get well_df safely

                if well_df is not None and not well_df.empty:
                    with st.spinner("Processing selected well-marker pairs..."):
                        for label in selected_labels:
                            try:
                                well_name, surface_name = label.split(" - ", 1)
                                # Find the row(s) in well_df matching this pair
                                pair_df = well_df[(well_df['Well'] == well_name) & (well_df['Surface'] == surface_name)]

                                if not pair_df.empty:
                                    # Assuming one row per unique well-marker pair label in well_df
                                    pair_data = pair_df.iloc[0].to_dict()
                                    selected_pairs_data.append(pair_data)

                                    # Find the nearest trace index for this pair's coordinates
                                    if st.session_state.header_loader:
                                        trace_idx = get_nearest_trace_index(st.session_state.header_loader, pair_data['X'], pair_data['Y'])
                                        if trace_idx is not None:
                                            all_selected_trace_indices.append(trace_idx)
                                        else:
                                            logging.warning(f"Could not find nearest trace for well-marker pair: {label}")
                                    else:
                                         st.error("SEG-Y headers not loaded. Cannot find trace indices.")
                                         st.stop() # Stop execution

                            except Exception as e:
                                logging.error(f"Error processing well-marker pair {label}: {e}")
                                st.error(f"Error processing well-marker pair {label}. See logs for details.")
                                st.stop() # Stop execution

                    # Store the structured data and aggregated indices
                    st.session_state.selected_well_marker_pairs = selected_pairs_data # Overwrite labels with structured data
                    st.session_state.selected_indices = list(set(all_selected_trace_indices)) # Store unique indices
                    st.session_state.area_selected_details = {'type': 'well_markers', 'count': len(selected_pairs_data), 'labels': selected_labels}
                    st.session_state.area_selected = True
                    st.session_state.area_selected_mode = 'well_markers'
                    st.session_state.selected_data_for_precompute = selected_pairs_data # Set data for precompute

                    # Set precomputation_complete to True to skip that step
                    st.session_state.precomputation_complete = True
                    logging.info("Setting precomputation_complete flag to True to skip precomputation step")

                    # Load trace data for selected well markers
                    loaded_trace_data = []
                    for idx, pair_data in enumerate(selected_pairs_data):
                        trace_idx = st.session_state.selected_indices[idx] if idx < len(st.session_state.selected_indices) else None
                        if trace_idx is not None:
                            # Load the trace sample
                            trace_sample = data_utils.load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)

                            # Create trace data dictionary
                            trace_data = {
                                'trace_sample': trace_sample,
                                'trace_idx': trace_idx,
                                'well_marker_name': f"{pair_data.get('Well', 'Unknown')} - {pair_data.get('Surface', 'Unknown')}",
                                'marker_value': pair_data.get('TWT_sec', None) if st.session_state.plot_twt else None
                            }
                            loaded_trace_data.append(trace_data)

                    # Store loaded trace data in session state
                    st.session_state.loaded_trace_data = loaded_trace_data

                    # Calculate HFC and Spectral Decrease percentile values for Option 1 workflow
                    # This is crucial since Option 1 skips the precompute_qc_page.py where these values are normally calculated
                    try:
                        from utils.processing import run_precomputation

                        # Extract trace samples for processing
                        original_traces = [item['trace_sample'] for item in loaded_trace_data]

                        # Use default precomputation parameters for Option 1
                        default_precomputation_params = {
                            "apply_smoothing": True,
                            "smoothing_window": 5,
                            "apply_normalization": True,
                            "normalization_method": "Max Amplitude",
                            "apply_filter": False,
                            "filter_type": None,
                            "filter_params": {}
                        }

                        # Run pre-computation to get descriptors
                        processed_traces = run_precomputation(original_traces, default_precomputation_params)

                        # Calculate HFC and Spectral Decrease percentile values
                        hfc_values = []
                        spec_decrease_values = []
                        for desc in processed_traces:
                            if hasattr(desc, 'get') and desc.get('hfc') is not None:
                                hfc_values.extend(desc['hfc'])
                            if hasattr(desc, 'get') and desc.get('spec_decrease') is not None:
                                spec_decrease_values.extend(desc['spec_decrease'])

                        if hfc_values:
                            # Get the user-configured HFC percentile (default to 95 if not specified)
                            hfc_percentile = st.session_state.plot_settings.get('hfc_percentile', 95.0)
                            hfc_pc = np.percentile(hfc_values, hfc_percentile)
                            # Store percentile cutoff value for normalization
                            st.session_state.plot_settings['hfc_pc'] = float(hfc_pc)
                            # Keep backward compatibility
                            st.session_state.plot_settings['hfc_p95'] = float(hfc_pc)
                            logging.info(f"Option 1: Calculated HFC percentile cutoff (p{hfc_percentile}): {hfc_pc}")

                        if spec_decrease_values:
                            # Get the user-configured Spectral Decrease percentile (default to 95 if not specified)
                            spec_decrease_percentile = st.session_state.plot_settings.get('spec_decrease_percentile', 95.0)
                            spec_decrease_pc = np.percentile(spec_decrease_values, spec_decrease_percentile)
                            # Store percentile cutoff value for normalization
                            st.session_state.plot_settings['spec_decrease_pc'] = float(spec_decrease_pc)
                            # Keep backward compatibility
                            st.session_state.plot_settings['spec_decrease_p95'] = float(spec_decrease_pc)
                            logging.info(f"Option 1: Calculated Spectral Decrease percentile cutoff (p{spec_decrease_percentile}): {spec_decrease_pc}")

                    except Exception as e:
                        logging.warning(f"Could not calculate percentile values for Option 1: {e}")
                        # Continue anyway - the robust helper functions will handle missing values

                    st.success(f"{len(selected_pairs_data)} well-marker pair(s) selected. Found {len(st.session_state.selected_indices)} traces. Proceeding to Analyze Data.")
                    st.session_state.current_step = "analyze_data" # Transition directly to analyze_data
                    st.rerun()

                else:
                    st.warning("Well data not loaded or empty. Cannot process well-marker selections.")
                    st.stop() # Stop execution

            elif st.session_state.selection_mode == "Single inline (all crosslines)":
                # Logic for Single Inline - need to get all trace indices for the selected inline
                if st.session_state.header_loader and st.session_state.get('selected_inline') is not None:
                    headers_df = pd.DataFrame({
                        'inline': st.session_state.header_loader.inlines,
                        'crossline': st.session_state.header_loader.crosslines,
                        'trace_idx': st.session_state.header_loader.unique_indices
                    })
                    selected_inline_df = headers_df[headers_df['inline'] == st.session_state.selected_inline]
                    st.session_state.selected_indices = selected_inline_df['trace_idx'].tolist()
                    st.session_state.area_selected_details = {'type': 'single_inline', 'inline': st.session_state.selected_inline, 'count': len(st.session_state.selected_indices)}
                    st.session_state.area_selected = True
                    st.session_state.area_selected_mode = 'single_inline'
                    st.session_state.selected_data_for_precompute = None # Or relevant data for this mode
                    # Set precomputation_complete to True to skip that step
                    st.session_state.precomputation_complete = True

                    st.success(f"Selected inline {st.session_state.selected_inline}. Found {len(st.session_state.selected_indices)} traces. Proceeding to Analyze Data.")
                    st.session_state.current_step = "analyze_data"
                    st.rerun()
                else:
                    st.warning("Please select an inline number.")
                    st.stop()

            elif st.session_state.selection_mode == "Single crossline (all inlines)":
                # Logic for Single Crossline - need to get all trace indices for the selected crossline
                if st.session_state.header_loader and st.session_state.get('selected_crossline') is not None:
                    headers_df = pd.DataFrame({
                        'inline': st.session_state.header_loader.inlines,
                        'crossline': st.session_state.header_loader.crosslines,
                        'trace_idx': st.session_state.header_loader.unique_indices
                    })
                    selected_crossline_df = headers_df[headers_df['crossline'] == st.session_state.selected_crossline]
                    st.session_state.selected_indices = selected_crossline_df['trace_idx'].tolist()
                    st.session_state.area_selected_details = {'type': 'single_crossline', 'crossline': st.session_state.selected_crossline, 'count': len(st.session_state.selected_indices)}
                    st.session_state.area_selected = True
                    st.session_state.area_selected_mode = 'single_crossline'
                    st.session_state.selected_data_for_precompute = None # Or relevant data for this mode
                    # Set precomputation_complete to True to skip that step
                    st.session_state.precomputation_complete = True

                    st.success(f"Selected crossline {st.session_state.selected_crossline}. Found {len(st.session_state.selected_indices)} traces. Proceeding to Analyze Data.")
                    st.session_state.current_step = "analyze_data"
                    st.rerun()
                else:
                    st.warning("Please select a crossline number.")
                    st.stop()

            elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
                # Logic for AOI - need to get all trace indices within the selected AOI bounds
                if st.session_state.header_loader and st.session_state.get('aoi_bounds') is not None:
                     headers_df = pd.DataFrame({
                        'inline': st.session_state.header_loader.inlines,
                        'crossline': st.session_state.header_loader.crosslines,
                        'trace_idx': st.session_state.header_loader.unique_indices
                    })
                     aoi_bounds = st.session_state.aoi_bounds
                     aoi_df = headers_df[
                        (headers_df['inline'] >= aoi_bounds['inline_min']) &
                        (headers_df['inline'] <= aoi_bounds['inline_max']) &
                        (headers_df['crossline'] >= aoi_bounds['xline_min']) &
                        (headers_df['crossline'] <= aoi_bounds['xline_max'])
                    ]
                     st.session_state.selected_indices = aoi_df['trace_idx'].tolist()
                     st.session_state.area_selected_details = {'type': 'aoi', 'bounds': aoi_bounds, 'count': len(st.session_state.selected_indices)}
                     st.session_state.area_selected = True
                     st.session_state.area_selected_mode = 'aoi'
                     st.session_state.selected_data_for_precompute = None # Or relevant data for this mode
                     # Set precomputation_complete to True to skip that step
                     st.session_state.precomputation_complete = True

                     st.success(f"Selected AOI. Found {len(st.session_state.selected_indices)} traces. Proceeding to Analyze Data.")
                     st.session_state.current_step = "analyze_data"
                     st.rerun()
                else:
                    st.warning("Please define the AOI bounds.")
                    st.stop()

            elif st.session_state.selection_mode == "By Polyline File Import":
                # Logic for Polyline - need to get trace indices near the polyline
                if st.session_state.header_loader and st.session_state.get('polyline_file_info') is not None and st.session_state.get('polyline_tolerance') is not None:
                    # Assuming polyline data is loaded and parsed elsewhere and stored in session state
                    # For now, we'll just check if the file info exists. The actual trace finding logic
                    # would need to be implemented or called here.
                    # Placeholder:
                    st.warning("Polyline trace selection logic not fully implemented yet.")
                    # Assuming selected_indices would be populated by a function call here
                    # st.session_state.selected_indices = find_traces_near_polyline(...)
                    # st.session_state.area_selected_details = {'type': 'polyline', 'file': st.session_state.polyline_file_info['name'], 'tolerance': st.session_state.polyline_tolerance, 'count': len(st.session_state.selected_indices)}
                    # st.session_state.area_selected = True
                    # st.session_state.area_selected_mode = 'polyline'
                    # st.session_state.selected_data_for_precompute = None # Or relevant data for this mode
                    # st.session_state.current_step = "precompute_qc"
                    # st.rerun()
                    st.stop() # Prevent transition until polyline logic is ready
                else:
                    st.warning("Please upload a polyline file and set tolerance.")
                    st.stop()

            else:
                st.warning("Unknown selection mode. Please select a valid mode.")
                st.stop()

        # Add a "Start New Analysis" button to the sidebar
        st.sidebar.markdown("---")
        if st.sidebar.button("🔄 Start New Analysis", use_container_width=True):
            reset_state()
            st.success("Starting new analysis. All temporary data has been cleared.")
            st.rerun()
        return

    # --- This section is removed as it's redundant with the mode selection above ---
    # The trace selection logic is now handled in the "Next: Analyze Data" button

    # Remove redundant sections - these are handled in the main mode selection above

    # Add a separator before the proceed button
    st.markdown("---")

    # Proceed Button with a more specific key and primary styling
    st.markdown("### Complete Step 3 and Continue")

    # Check if display_params_configured is set, if not, set it to True if stats_defaults is available
    if not st.session_state.get('display_params_configured') and st.session_state.get('stats_defaults') is not None:
        logging.info("Setting display_params_configured to True as stats_defaults is available")
        st.session_state.display_params_configured = True

    # Log current state for debugging
    logging.info(f"Before button click - display_params_configured: {st.session_state.get('display_params_configured')}")
    logging.info(f"Before button click - area_selected: {st.session_state.get('area_selected')}")

    if st.button("Next: Analyze Data", key="select_area_next_button", use_container_width=True, type="primary"):
        logging.info("Proceed button clicked in Select Area page")
        with st.spinner("Preparing selected data..."):
            # Determine selected indices based on selection mode
            if st.session_state.selection_mode == "By well markers":
                # Check if well analysis sub-option is selected
                if not st.session_state.get('well_analysis_sub_option'):
                    st.warning("Please select an analysis type (Individual Plots or Comparative Plots) before proceeding.")
                    st.stop()

                if st.session_state.get('selected_well_marker_pairs') and isinstance(st.session_state.selected_well_marker_pairs, list):
                    temp_selected_indices = []
                    temp_selected_data_for_precompute = []
                    well_df = st.session_state.well_df

                    for pair_label in st.session_state.selected_well_marker_pairs:
                        well_name, surface_name = pair_label.split(" - ")
                        # Find the original data for this pair
                        marker_data_row = well_df[
                            (well_df['Well'] == well_name) & (well_df['Surface'] == surface_name)
                        ]
                        if not marker_data_row.empty:
                            marker_info = marker_data_row.iloc[0].to_dict()
                            trace_idx = get_nearest_trace_index(
                                st.session_state.header_loader,
                                marker_info["X"],
                                marker_info["Y"]
                            )
                            if trace_idx is not None:
                                temp_selected_indices.append(trace_idx)
                                # Store relevant info for precomputation, e.g., the marker_info dict
                                # and the trace_idx itself.
                                temp_selected_data_for_precompute.append({
                                    "well_name": well_name,
                                    "surface_name": surface_name,
                                    "x_coord": marker_info["X"],
                                    "y_coord": marker_info["Y"],
                                    "z_coord": marker_info.get("Z"), # Or TWT_sec if used
                                    "trace_index": trace_idx,
                                    "original_marker_data": marker_info
                                })

                    # Remove duplicate trace indices if necessary, though for well markers,
                    # different pairs usually mean different locations.
                    st.session_state.selected_indices = list(set(temp_selected_indices))
                    st.session_state.selected_data_for_precompute = temp_selected_data_for_precompute

                    # Always set area_selected to True if we have selected well-marker pairs,
                    # even if no valid traces were found (we'll handle this in precompute_qc)
                    st.session_state.area_selected = True
                    st.session_state.area_selected_mode = 'well_markers'

                    st.session_state.area_selected_details = {
                        'type': 'well_markers',
                        'count': len(st.session_state.selected_indices),
                        'pairs_count': len(st.session_state.selected_well_marker_pairs),
                        'analysis_type': st.session_state.well_analysis_sub_option
                    }

                    # Set precomputation_complete to True to skip that step
                    st.session_state.precomputation_complete = True
                    logging.info("Setting precomputation_complete flag to True to skip precomputation step")

                    # Calculate HFC and Spectral Decrease percentile values for Option 1 workflow (general path)
                    # This is crucial since Option 1 skips the precompute_qc_page.py where these values are normally calculated
                    if st.session_state.selected_indices:
                        try:
                            from utils.processing import run_precomputation
                            import numpy as np

                            # Load a sample of traces for percentile calculation (limit to 10 for performance)
                            sample_indices = st.session_state.selected_indices[:min(10, len(st.session_state.selected_indices))]
                            loaded_trace_data = []
                            for trace_idx in sample_indices:
                                try:
                                    trace_sample = data_utils.load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)
                                    if trace_sample is not None and len(trace_sample) > 0:
                                        loaded_trace_data.append({'trace_sample': trace_sample, 'trace_idx': trace_idx})
                                except Exception as e:
                                    logging.warning(f"Could not load trace {trace_idx} for percentile calculation: {e}")

                            if loaded_trace_data:
                                # Extract trace samples for processing
                                original_traces = [item['trace_sample'] for item in loaded_trace_data]

                                # Use default precomputation parameters for Option 1
                                default_precomputation_params = {
                                    "apply_smoothing": True,
                                    "smoothing_window": 5,
                                    "apply_normalization": True,
                                    "normalization_method": "Max Amplitude",
                                    "apply_filter": False,
                                    "filter_type": None,
                                    "filter_params": {}
                                }

                                # Run pre-computation to get descriptors
                                processed_traces = run_precomputation(original_traces, default_precomputation_params)

                                # Calculate HFC and Spectral Decrease percentile values
                                hfc_values = []
                                spec_decrease_values = []
                                for desc in processed_traces:
                                    if hasattr(desc, 'get') and desc.get('hfc') is not None:
                                        hfc_values.extend(desc['hfc'])
                                    if hasattr(desc, 'get') and desc.get('spec_decrease') is not None:
                                        spec_decrease_values.extend(desc['spec_decrease'])

                                if hfc_values:
                                    # Get the user-configured HFC percentile (default to 95 if not specified)
                                    hfc_percentile = st.session_state.plot_settings.get('hfc_percentile', 95.0)
                                    hfc_pc = np.percentile(hfc_values, hfc_percentile)
                                    # Store percentile cutoff value for normalization
                                    st.session_state.plot_settings['hfc_pc'] = float(hfc_pc)
                                    # Keep backward compatibility
                                    st.session_state.plot_settings['hfc_p95'] = float(hfc_pc)
                                    logging.info(f"Option 1 (general): Calculated HFC percentile cutoff (p{hfc_percentile}): {hfc_pc}")

                                if spec_decrease_values:
                                    # Get the user-configured Spectral Decrease percentile (default to 95 if not specified)
                                    spec_decrease_percentile = st.session_state.plot_settings.get('spec_decrease_percentile', 95.0)
                                    spec_decrease_pc = np.percentile(spec_decrease_values, spec_decrease_percentile)
                                    # Store percentile cutoff value for normalization
                                    st.session_state.plot_settings['spec_decrease_pc'] = float(spec_decrease_pc)
                                    # Keep backward compatibility
                                    st.session_state.plot_settings['spec_decrease_p95'] = float(spec_decrease_pc)
                                    logging.info(f"Option 1 (general): Calculated Spectral Decrease percentile cutoff (p{spec_decrease_percentile}): {spec_decrease_pc}")

                        except Exception as e:
                            logging.warning(f"Could not calculate percentile values for Option 1 (general path): {e}")
                            # Continue anyway - the robust helper functions will handle missing values

                    if st.session_state.selected_indices:
                        st.success(f"{len(st.session_state.selected_well_marker_pairs)} well-marker pair(s) selected, resulting in {len(st.session_state.selected_indices)} unique traces.")
                    else:
                        # Still proceed, but show a warning
                        st.warning("No valid traces found for the selected well-marker pairs. You can still proceed to the next step, but you may need to adjust your selection.")
                else:
                    st.warning("Please select at least one well-marker pair to continue.")
                    st.stop()

            elif st.session_state.selection_mode == "Single inline (all crosslines)":
                # Single inline mode - find all traces with the selected inline
                selected_indices = []
                if st.session_state.header_loader:
                    # Get all traces with the selected inline
                    inlines = st.session_state.header_loader.inlines
                    unique_indices = st.session_state.header_loader.unique_indices

                    # Find indices where inline matches selected inline
                    for i, inline in enumerate(inlines):
                        if inline == st.session_state.selected_inline:
                            selected_indices.append(unique_indices[i])

                st.session_state.selected_indices = selected_indices

                # Store selected data for pre-computation
                st.session_state.selected_data_for_precompute = {
                    "mode": "single_inline",
                    "selected_inline": st.session_state.selected_inline,
                    "batch_size": st.session_state.batch_size,
                    "selected_indices": selected_indices
                }

            elif st.session_state.selection_mode == "Single crossline (all inlines)":
                # Single crossline mode - find all traces with the selected crossline
                selected_indices = []
                if st.session_state.header_loader:
                    # Get all traces with the selected crossline
                    crosslines = st.session_state.header_loader.crosslines
                    unique_indices = st.session_state.header_loader.unique_indices

                    # Find indices where crossline matches selected crossline
                    for i, crossline in enumerate(crosslines):
                        if crossline == st.session_state.selected_crossline:
                            selected_indices.append(unique_indices[i])

                st.session_state.selected_indices = selected_indices

                # Store selected data for pre-computation
                st.session_state.selected_data_for_precompute = {
                    "mode": "single_crossline",
                    "selected_crossline": st.session_state.selected_crossline,
                    "batch_size": st.session_state.batch_size,
                    "selected_indices": selected_indices
                }

            elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
                # AOI mode - find all traces within the AOI bounds
                selected_indices = []
                if st.session_state.header_loader:
                    # Get header dataframe
                    headers_df = pd.DataFrame({
                        'inline': st.session_state.header_loader.inlines,
                        'crossline': st.session_state.header_loader.crosslines,
                        'x': st.session_state.header_loader.x_coords,
                        'y': st.session_state.header_loader.y_coords
                    })
                    headers_df['trace_idx'] = st.session_state.header_loader.unique_indices

                    # Filter by inline/crossline range
                    aoi_df = headers_df[
                        (headers_df['inline'] >= st.session_state.aoi_inline_min) &
                        (headers_df['inline'] <= st.session_state.aoi_inline_max) &
                        (headers_df['crossline'] >= st.session_state.aoi_xline_min) &
                        (headers_df['crossline'] <= st.session_state.aoi_xline_max)
                    ]

                    selected_indices = aoi_df['trace_idx'].tolist()

                st.session_state.selected_indices = selected_indices

                # Store selected data for pre-computation
                st.session_state.selected_data_for_precompute = {
                    "mode": "aoi",
                    "aoi_inline_min": st.session_state.aoi_inline_min,
                    "aoi_inline_max": st.session_state.aoi_inline_max,
                    "aoi_xline_min": st.session_state.aoi_xline_min,
                    "aoi_xline_max": st.session_state.aoi_xline_max,
                    "aoi_processing_option": st.session_state.aoi_processing_option,
                    "selected_indices": selected_indices
                }

            elif st.session_state.selection_mode == "By Polyline File Import":
                # Polyline mode - find traces near the polyline
                selected_indices = []
                if st.session_state.polyline_file_info and st.session_state.header_loader:
                    try:
                        # Parse polyline file
                        polyline_file = st.session_state.polyline_file_info['buffer']
                        polyline_vertices = parse_polyline_string(polyline_file.getvalue().decode('utf-8'))

                        if polyline_vertices:
                            # Find traces near polyline
                            x_coords = st.session_state.header_loader.x_coords
                            y_coords = st.session_state.header_loader.y_coords
                            unique_indices = st.session_state.header_loader.unique_indices

                            selected_indices = find_traces_near_polyline(
                                polyline_vertices,
                                x_coords,
                                y_coords,
                                unique_indices,
                                tolerance=st.session_state.polyline_tolerance
                            )
                    except Exception as e:
                        st.error(f"Error processing polyline: {e}")
                        logging.error(f"Polyline processing failed: {e}", exc_info=True)

                st.session_state.selected_indices = selected_indices

                # Store selected data for pre-computation
                st.session_state.selected_data_for_precompute = {
                    "mode": "polyline",
                    "polyline_file_info": st.session_state.polyline_file_info,
                    "polyline_tolerance": st.session_state.polyline_tolerance,
                    "selected_indices": selected_indices
                }

            # Log the area_selected status
            logging.info(f"Area selected flag is {st.session_state.area_selected}. Selected {len(st.session_state.selected_indices)} traces.")

            # Ensure display_params_configured is set to True if stats_defaults is available
            if not st.session_state.get('display_params_configured') and st.session_state.get('stats_defaults') is not None:
                logging.info("Setting display_params_configured to True as stats_defaults is available")
                st.session_state.display_params_configured = True

            # Show success message with number of selected traces
            st.success(f"Selected {len(st.session_state.selected_indices)} traces for analysis.")

            # Explicitly ensure we have the necessary session state variables set
            if 'selected_indices' not in st.session_state or not st.session_state.selected_indices:
                logging.warning("No traces selected. Cannot proceed to next step.")
                st.error("No traces were selected. Please check your selection criteria and try again.")
                return

            # Log all important session state variables before transitioning
            logging.info(f"Before transition - display_params_configured: {st.session_state.get('display_params_configured')}")
            logging.info(f"Before transition - area_selected: {st.session_state.get('area_selected')}")
            logging.info(f"Before transition - selected_data_for_precompute: {st.session_state.get('selected_data_for_precompute') is not None}")
            logging.info(f"Before transition - selected_indices count: {len(st.session_state.get('selected_indices', []))}")

            # Navigate directly to analyze_data step
            logging.info("Transitioning to analyze_data step")
            st.session_state.current_step = "analyze_data"
            st.rerun()

    # Add a "Start New Analysis" button to the sidebar
    st.sidebar.markdown("---")
    if st.sidebar.button("🔄 Start New Analysis", use_container_width=True):
        reset_state()
        st.success("Starting new analysis. All temporary data has been cleared.")
        st.rerun()
